from typing import Optional, TYPE_CHECKING
from sqlalchemy import Sequence, select, and_, or_
from utils.modules.core.db.models import Hub, HubModerator, Role, Message, Broadcast

if TYPE_CHECKING:
    from main import Bot


async def get_user_moderated_hubs(bot: "Bot", user_id: str):
    """
    Get all hubs where the user has Moderator or Manager permissions.
    
    Args:
        bot: The bot instance for database access
        user_id: The Discord user ID to check permissions for
        
    Returns:
        List of Hub objects where the user has moderation permissions
    """
    async with bot.db.get_session() as session:
        stmt = (
            select(Hub)
            .join(HubModerator, Hub.id == HubModerator.hubId, isouter=True)
            .where(
                or_(
                    Hub.ownerId == user_id,
                    and_(
                        HubModerator.userId == user_id,
                        HubModerator.role.in_([Role.MODERATOR, Role.MANAGER])
                    )
                )
            )
        )
        result = await session.execute(stmt)
        return result.scalars().all()


async def fetch_original_message(bot: "Bo<PERSON>", message_id: str) -> Optional[Message]:
    """
    Fetch original message, first directly then through broadcast relationship.
    
    This function implements the message priority logic:
    1. First, try to fetch the original message directly from the database
    2. If not found, fetch the broadcast record and get the original message through the relationship
    
    Args:
        bot: The bot instance for database access
        message_id: The Discord message ID to fetch
        
    Returns:
        Message object if found, None otherwise
    """
    async with bot.db.get_session() as session:
        # First try to fetch the message directly
        stmt = select(Message).where(Message.id == message_id)
        result = await session.execute(stmt)
        message = result.scalar_one_or_none()
        
        if message:
            return message
        
        # If not found, try to fetch through broadcast relationship
        stmt = (
            select(Message)
            .join(Broadcast, Message.id == Broadcast.messageId)
            .where(Broadcast.id == message_id)
        )
        result = await session.execute(stmt)
        return result.scalar_one_or_none()
