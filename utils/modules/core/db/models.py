from typing import List, Optional
from sqlalchemy import (
    Column,
    Integer,
    String,
    Boolean,
    DateTime,
    Float,
    <PERSON><PERSON><PERSON>,
    JSO<PERSON>,
    Enum as SQLEnum,
    Table,
    Text,
    UniqueConstraint,
    Index,
    func,
)
from sqlalchemy.orm import (
    relationship,
    Mapped,
    mapped_column,
    DeclarativeBase,
    MappedAsDataclass,
)
from sqlalchemy.dialects.postgresql import ARRAY
from datetime import datetime, timezone
import enum
from nanoid import generate


# Enums
class Role(enum.Enum):
    MODERATOR = "MODERATOR"
    MANAGER = "MANAGER"


class HubActivityLevel(enum.Enum):
    LOW = "LOW"  # <10 messages/day
    MEDIUM = "MEDIUM"  # 10-100 messages/day
    HIGH = "HIGH"  # >100 messages/day


class InfractionType(enum.Enum):
    BLACKLIST = "BLACKLIST"
    WARNING = "WARNING"


class InfractionStatus(enum.Enum):
    ACTIVE = "ACTIVE"
    REVOKED = "REVOKED"
    APPEALED = "APPEALED"


class AppealStatus(enum.Enum):
    PENDING = "PENDING"
    ACCEPTED = "ACCEPTED"
    REJECTED = "REJECTED"


class BlockWordAction(enum.Enum):
    BLOCK_MESSAGE = "BLOCK_MESSAGE"
    SEND_ALERT = "SEND_ALERT"
    WARN = "WARN"
    MUTE = "MUTE"  # Temporary mute (with expiry)
    BLACKLIST = "BLACKLIST"  # Permanent blacklist (no expiry)


class CallRatingStatus(enum.Enum):
    LIKE = "LIKE"
    DISLIKE = "DISLIKE"


class Badges(enum.Enum):
    VOTER = "VOTER"  # For users who have voted on top.gg
    SUPPORTER = "SUPPORTER"  # $1.99/month
    TRANSLATOR = "TRANSLATOR"  # For users who help translate InterChat
    DEVELOPER = "DEVELOPER"  # For users who contribute to the codebase
    STAFF = "STAFF"  # For staff members who help manage the platform
    BETA_TESTER = "BETA_TESTER"  # For users who participated in beta testing


class CallStatus(enum.Enum):
    QUEUED = "QUEUED"
    ACTIVE = "ACTIVE"
    ENDED = "ENDED"


class ReportStatus(enum.Enum):
    PENDING = "PENDING"
    RESOLVED = "RESOLVED"
    IGNORED = "IGNORED"


class BanType(enum.Enum):
    PERMANENT = "PERMANENT"
    TEMPORARY = "TEMPORARY"


class BanStatus(enum.Enum):
    ACTIVE = "ACTIVE"
    EXPIRED = "EXPIRED"
    REVOKED = "REVOKED"


class LeaderboardPeriod(enum.Enum):
    DAILY = "DAILY"
    WEEKLY = "WEEKLY"
    MONTHLY = "MONTHLY"
    ALL_TIME = "ALL_TIME"


class LeaderboardType(enum.Enum):
    USER = "USER"
    SERVER = "SERVER"
    HUB = "HUB"


class Base(MappedAsDataclass, DeclarativeBase):
    pass


# Association table for Hub-Tag many-to-many relationship
hub_tags = Table(
    "hub_tags",
    Base.metadata,
    Column("hubId", String, ForeignKey("Hub.id"), primary_key=True),
    Column("tagId", String, ForeignKey("Tag.id"), primary_key=True),
)


class Hub(Base):
    __tablename__ = "Hub"

    # Primary fields
    id: Mapped[str] = mapped_column(
        String, primary_key=True, default=lambda: generate(), init=False
    )
    name: Mapped[str] = mapped_column(String, unique=True)
    description: Mapped[str] = mapped_column(Text)
    ownerId: Mapped[str] = mapped_column(String, ForeignKey("User.id"))
    iconUrl: Mapped[str] = mapped_column(String)
    shortDescription: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)

    # Relationships
    owner: Mapped["User"] = relationship(
        "User", back_populates="ownedHubs", lazy="noload", init=False
    )
    rulesAcceptances: Mapped[list["HubRulesAcceptance"]] = relationship(
        "HubRulesAcceptance", back_populates="hub", lazy="noload", init=False
    )
    moderators: Mapped[list["HubModerator"]] = relationship(
        "HubModerator", back_populates="hub", lazy="noload", init=False
    )
    connections: Mapped[list["Connection"]] = relationship(
        "Connection", back_populates="hub", lazy="noload", init=False
    )
    tags: Mapped[list["Tag"]] = relationship(
        "Tag", secondary=hub_tags, back_populates="hubs", lazy="noload", init=False
    )
    upvotes: Mapped[list["HubUpvote"]] = relationship(
        "HubUpvote", back_populates="hub", lazy="noload", init=False
    )
    reviews: Mapped[list["HubReview"]] = relationship(
        "HubReview", back_populates="hub", lazy="noload", init=False
    )
    logConfig: Mapped["HubLogConfig | None"] = relationship(
        "HubLogConfig", back_populates="hub", lazy="noload", init=False
    )
    blockWords: Mapped[list["BlockWord"]] = relationship(
        "BlockWord", back_populates="hub", lazy="noload", init=False
    )
    antiSwearRules: Mapped[list["AntiSwearRule"]] = relationship(
        "AntiSwearRule", back_populates="hub", lazy="noload", init=False
    )
    infractions: Mapped[list["Infraction"]] = relationship(
        "Infraction", back_populates="hub", lazy="noload", init=False
    )
    invites: Mapped[list["HubInvite"]] = relationship(
        "HubInvite", back_populates="hub", lazy="noload", init=False
    )
    messages: Mapped[list["Message"]] = relationship(
        "Message", back_populates="hub", lazy="noload", init=False
    )
    reports: Mapped[list["Report"]] = relationship(
        "Report", back_populates="hub", lazy="noload", init=False
    )
    activityMetrics: Mapped["HubActivityMetrics | None"] = relationship(
        "HubActivityMetrics", back_populates="hub", lazy="noload", init=False
    )
    moderationLogs: Mapped[list["ModerationLog"]] = relationship(
        "ModerationLog", back_populates="hub", lazy="noload", init=False
    )
    leaderboardEntries: Mapped[list["LeaderboardEntry"]] = relationship(
        "LeaderboardEntry", back_populates="hub", lazy="noload", init=False
    )

    # Timestamps
    createdAt: Mapped[datetime] = mapped_column(
        DateTime, server_default=func.now(), nullable=False
    )
    updatedAt: Mapped[datetime] = mapped_column(
        DateTime, server_default=func.now(), onupdate=func.now(), nullable=False
    )
    lastActive: Mapped[datetime] = mapped_column(
        DateTime, server_default=func.now(), nullable=False
    )

    # Optional fields
    bannerUrl: Mapped[Optional[str]] = mapped_column(
        String, nullable=True, default=None
    )
    welcomeMessage: Mapped[Optional[str]] = mapped_column(
        Text, nullable=True, default=None
    )
    language: Mapped[Optional[str]] = mapped_column(String, nullable=True, default=None)
    region: Mapped[Optional[str]] = mapped_column(String, nullable=True, default=None)

    # Numeric fields
    settings: Mapped[int] = mapped_column(Integer, nullable=False, default=0)
    appealCooldownHours: Mapped[int] = mapped_column(
        Integer, default=168, nullable=False
    )
    weeklyMessageCount: Mapped[int] = mapped_column(Integer, default=0, nullable=False)

    # Boolean flags
    private: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    locked: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    nsfw: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    verified: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    partnered: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    featured: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)

    # Array field (PostgreSQL specific)
    rules: Mapped[list[str]] = mapped_column(
        ARRAY(String), default=list, nullable=False
    )

    # Enum field
    activityLevel: Mapped[HubActivityLevel] = mapped_column(
        default=HubActivityLevel.LOW, nullable=False
    )

    # Indexes
    __table_args__ = (
        Index("ix_hub_ownerId", "ownerId"),
        Index("ix_hub_verified_featured_private", "verified", "featured", "private"),
        Index("ix_hub_activityLevel", "activityLevel"),
        Index("ix_hub_language", "language"),
        Index("ix_hub_nsfw", "nsfw"),
        Index("ix_hub_weeklyMessageCount", "weeklyMessageCount"),
    )


class Tag(Base):
    __tablename__ = "Tag"

    id: Mapped[str] = mapped_column(
        String, primary_key=True, default=lambda: generate(), init=False
    )
    name: Mapped[str] = mapped_column(String, unique=True, nullable=False)
    category: Mapped[Optional[str]] = mapped_column(
        String, nullable=True
    )  # e.g., "Gaming", "Technology", "Art", "Music"
    description: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    color: Mapped[Optional[str]] = mapped_column(
        String, nullable=True
    )  # Hex color for UI display

    # Many-to-many relationship with Hub
    hubs: Mapped[list["Hub"]] = relationship(
        "Hub", secondary=hub_tags, back_populates="tags", lazy="noload"
    )

    createdAt: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())
    isOfficial: Mapped[bool] = mapped_column(
        Boolean, default=False
    )  # Official InterChat tags vs user-created
    usageCount: Mapped[int] = mapped_column(
        Integer, default=0
    )  # Track popularity for autocomplete

    __table_args__ = (
        Index("ix_tag_category", "category"),
        Index("ix_tag_usageCount", "usageCount"),
    )


class HubUpvote(Base):
    __tablename__ = "HubUpvote"

    id: Mapped[str] = mapped_column(
        String, primary_key=True, default=lambda: generate(), init=False
    )
    userId: Mapped[str] = mapped_column(String, ForeignKey("User.id"), nullable=False)
    createdAt: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())
    hubId: Mapped[str] = mapped_column(
        String, ForeignKey("Hub.id", ondelete="CASCADE"), nullable=False, default=None
    )

    hub: Mapped["Hub"] = relationship(
        "Hub", back_populates="upvotes", lazy="noload", init=False
    )
    user: Mapped["User"] = relationship(
        "User", back_populates="upvotedHubs", lazy="noload", init=False
    )

    __table_args__ = (
        UniqueConstraint("hubId", "userId"),
        Index("ix_hub_upvote_user_id", "userId"),
    )


class HubReview(Base):
    __tablename__ = "HubReview"

    id: Mapped[str] = mapped_column(
        String, primary_key=True, default=lambda: generate(), init=False
    )
    createdAt: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())
    updatedAt: Mapped[datetime] = mapped_column(
        DateTime, server_default=func.now(), onupdate=func.now()
    )
    hubId: Mapped[str] = mapped_column(
        String, ForeignKey("Hub.id", ondelete="CASCADE"), nullable=False, default=None
    )
    userId: Mapped[str] = mapped_column(
        String, ForeignKey("User.id"), nullable=False, default=None
    )
    rating: Mapped[int] = mapped_column(Integer, default=None)  # Rating from 1 to 5
    text: Mapped[str] = mapped_column(String, default=None)  # Review text

    hub: Mapped["Hub"] = relationship(
        "Hub", back_populates="reviews", lazy="noload", init=False
    )
    user: Mapped["User"] = relationship(
        "User", back_populates="reviews", lazy="noload", init=False
    )

    __table_args__ = (
        UniqueConstraint("hubId", "userId"),
        Index("ix_hub_review_user_id", "userId"),
    )


class HubModerator(Base):
    __tablename__ = "HubModerator"

    id: Mapped[str] = mapped_column(
        String, primary_key=True, default=lambda: generate(), init=False
    )
    hubId: Mapped[str] = mapped_column(
        String, ForeignKey("Hub.id", ondelete="CASCADE"), default=None
    )
    userId: Mapped[str] = mapped_column(String, ForeignKey("User.id"), default=None)
    role: Mapped[str] = mapped_column(
        SQLEnum(Role, name="Role", skip_create=True), default=None
    )

    hub: Mapped["Hub"] = relationship(
        "Hub", back_populates="moderators", lazy="noload", init=False
    )
    user: Mapped["User"] = relationship(
        "User", back_populates="modPositions", lazy="noload", init=False
    )

    __table_args__ = (
        UniqueConstraint("hubId", "userId"),
        Index("ix_hub_moderator_user_id", "userId"),
    )


class Connection(Base):
    __tablename__ = "Connection"

    id: Mapped[str] = mapped_column(
        String, primary_key=True, default=lambda: generate(), init=False
    )
    channelId: Mapped[str] = mapped_column(String, unique=True)
    invite: Mapped[Optional[str]] = mapped_column(String)
    webhookUrl: Mapped[str] = mapped_column(String)
    serverId: Mapped[str] = mapped_column(String, ForeignKey("ServerData.id"))
    hubId: Mapped[str] = mapped_column(String, ForeignKey("Hub.id", ondelete="CASCADE"))
    createdAt: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())
    lastActive: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())
    parentId: Mapped[Optional[str]] = mapped_column(
        String, default=None
    )  # Parent channel ID for threads

    connected: Mapped[bool] = mapped_column(Boolean, default=True)
    server: Mapped["ServerData"] = relationship(
        "ServerData", back_populates="connections", lazy="noload", init=False
    )
    hub: Mapped["Hub"] = relationship(
        "Hub", back_populates="connections", lazy="noload", init=False
    )

    __table_args__ = (
        UniqueConstraint("channelId", "serverId"),
        UniqueConstraint("hubId", "serverId"),
        Index("ix_connection_server_id", "serverId"),
        Index("ix_connection_hub_id", "hubId"),
        Index("ix_connection_hub_channel", "hubId", "channelId"),
        Index("ix_connection_channel_connected", "channelId", "connected"),
        Index("ix_connection_hub_connected", "hubId", "connected"),
        Index("ix_connection_last_active", "lastActive"),
    )


class Infraction(Base):
    __tablename__ = "Infraction"

    id: Mapped[str] = mapped_column(
        String, primary_key=True, default=lambda: generate(size=10), init=False
    )
    hubId: Mapped[str] = mapped_column(String, ForeignKey("Hub.id"))
    moderatorId: Mapped[str] = mapped_column(String, ForeignKey("User.id"))
    reason: Mapped[str] = mapped_column(String)
    expiresAt: Mapped[datetime] = mapped_column(DateTime)
    moderationCommand: Mapped[str] = mapped_column(
        String
    )  # TODO: Command that triggered the infraction
    logId: Mapped[str] = mapped_column(String)  # TODO: Link to moderation logs

    # For user infractions
    userId: Mapped[Optional[str]] = mapped_column(
        String, ForeignKey("User.id"), nullable=True
    )

    # For server infractions
    serverId: Mapped[Optional[str]] = mapped_column(
        String, ForeignKey("ServerData.id"), nullable=True
    )
    serverName: Mapped[Optional[str]] = mapped_column(String, nullable=True)

    hub: Mapped["Hub"] = relationship(
        "Hub", back_populates="infractions", lazy="noload", init=False
    )
    moderator: Mapped["User"] = relationship(
        "User",
        foreign_keys=[moderatorId],
        back_populates="issuedInfractions",
        lazy="noload",
        init=False,
    )
    user: Mapped["User"] = relationship(
        "User",
        foreign_keys=[userId],
        back_populates="infractions",
        lazy="noload",
        init=False,
    )
    server: Mapped["ServerData"] = relationship(
        "ServerData", back_populates="infractions", lazy="noload", init=False
    )
    appeals: Mapped[list["Appeal"]] = relationship(
        "Appeal", back_populates="infraction", lazy="noload", init=False
    )
    createdAt: Mapped[datetime] = mapped_column(
        DateTime, server_default=func.now(), default=None
    )
    updatedAt: Mapped[datetime] = mapped_column(
        DateTime, server_default=func.now(), onupdate=func.now(), default=None
    )

    type: Mapped[InfractionType] = mapped_column(
        SQLEnum(InfractionType), default=InfractionType.BLACKLIST
    )
    status: Mapped[InfractionStatus] = mapped_column(
        SQLEnum(InfractionStatus), default=InfractionStatus.ACTIVE
    )
    notified: Mapped[bool] = mapped_column(Boolean, default=False)

    __table_args__ = (
        Index("ix_infraction_status_hub", "status", "hubId"),
        Index("ix_infraction_user_id", "userId"),
        Index("ix_infraction_server_id", "serverId"),
        Index("ix_infraction_type", "type"),
        Index("ix_infraction_expires_at", "expiresAt"),
    )


class Appeal(Base):
    __tablename__ = "Appeal"

    infractionId: Mapped[str] = mapped_column(String, ForeignKey("Infraction.id"))
    userId: Mapped[str] = mapped_column(String, ForeignKey("User.id"))
    reason: Mapped[str] = mapped_column(String)
    infraction: Mapped["Infraction"] = relationship(
        "Infraction", back_populates="appeals", lazy="noload"
    )
    user: Mapped["User"] = relationship("User", back_populates="appeals", lazy="noload")
    createdAt: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())
    updatedAt: Mapped[datetime] = mapped_column(
        DateTime, server_default=func.now(), onupdate=func.now()
    )
    id: Mapped[str] = mapped_column(
        String, primary_key=True, default=lambda: generate(), init=False
    )
    status: Mapped[AppealStatus] = mapped_column(
        SQLEnum(AppealStatus), default=AppealStatus.PENDING
    )

    __table_args__ = (
        Index("ix_appeal_infraction_id", "infractionId"),
        Index("ix_appeal_user_id", "userId"),
        Index("ix_appeal_status", "status"),
        Index("ix_appeal_created_at", "createdAt"),
    )


class BlockWord(Base):
    __tablename__ = "BlockWord"

    id: Mapped[str] = mapped_column(
        String, primary_key=True, default=lambda: generate(), init=False
    )
    hubId: Mapped[str] = mapped_column(
        String, ForeignKey("Hub.id", ondelete="CASCADE"), nullable=False
    )
    name: Mapped[str] = mapped_column(String, nullable=False)
    createdBy: Mapped[str] = mapped_column(
        String, ForeignKey("User.id"), nullable=False
    )
    createdAt: Mapped[datetime] = mapped_column(
        DateTime, server_default=func.now(), init=False
    )
    updatedAt: Mapped[datetime] = mapped_column(
        DateTime,
        server_default=func.now(),
        onupdate=func.now(),
        init=False,
    )

    # Relations
    words: Mapped[str] = mapped_column(String, nullable=False)  # separated by comma (,)
    actions: Mapped[list[BlockWordAction]] = mapped_column(
        ARRAY(SQLEnum(BlockWordAction, name="BlockWordAction", create_type=False))
    )

    hub: Mapped["Hub"] = relationship("Hub", back_populates="blockWords", lazy="noload")
    creator: Mapped["User"] = relationship(
        "User", back_populates="blockWordsCreated", lazy="noload"
    )

    __table_args__ = (
        UniqueConstraint("hubId", "name"),
        Index("ix_block_word_hub_id", "hubId"),
    )


class AntiSwearRule(Base):
    __tablename__ = "AntiSwearRule"

    id: Mapped[str] = mapped_column(
        String, primary_key=True, default=lambda: generate(), init=False
    )
    hubId: Mapped[str] = mapped_column(
        String, ForeignKey("Hub.id", ondelete="CASCADE"), nullable=False
    )
    name: Mapped[str] = mapped_column(String, nullable=False)
    createdBy: Mapped[str] = mapped_column(
        String, ForeignKey("User.id"), nullable=False
    )
    # Duration configuration for time-based actions (in minutes)
    muteDurationMinutes: Mapped[Optional[int]] = mapped_column(
        Integer, nullable=True
    )  # Duration for MUTE action
    blacklistDurationMinutes: Mapped[Optional[int]] = mapped_column(
        Integer, nullable=True
    )  # Duration for BLACKLIST action
    actions: Mapped[list[BlockWordAction]] = mapped_column(
        ARRAY(SQLEnum(BlockWordAction, name="BlockWordAction", create_type=False))
    )
    createdAt: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())
    updatedAt: Mapped[datetime] = mapped_column(
        DateTime, server_default=func.now(), onupdate=func.now()
    )
    logViolations: Mapped[bool] = mapped_column(
        Boolean, default=False
    )  # Enable/disable logging

    hub: Mapped["Hub"] = relationship(
        "Hub", back_populates="antiSwearRules", lazy="noload", init=False
    )
    creator: Mapped["User"] = relationship(
        "User", back_populates="antiSwearRulesCreated", lazy="noload", init=False
    )
    patterns: Mapped[list["AntiSwearPattern"]] = relationship(
        "AntiSwearPattern",
        back_populates="rule",
        lazy="noload",
        cascade="all, delete-orphan",
        init=False,
    )

    __table_args__ = (
        UniqueConstraint("hubId", "name"),
        Index("ix_anti_swear_rule_hub_id", "hubId"),
    )


class AntiSwearPattern(Base):
    __tablename__ = "AntiSwearPattern"

    id: Mapped[str] = mapped_column(
        String, primary_key=True, default=lambda: generate(), init=False
    )
    ruleId: Mapped[str] = mapped_column(
        String, ForeignKey("AntiSwearRule.id", ondelete="CASCADE"), nullable=False
    )
    pattern: Mapped[str] = mapped_column(
        String, nullable=False
    )  # Individual word or pattern
    isRegex: Mapped[bool] = mapped_column(
        Boolean, default=False
    )  # For future extensibility

    rule: Mapped["AntiSwearRule"] = relationship(
        "AntiSwearRule", back_populates="patterns", lazy="noload", init=False
    )

    __table_args__ = (Index("ix_anti_swear_pattern_rule_id", "ruleId"),)


class HubLogConfig(Base):
    __tablename__ = "HubLogConfig"

    id: Mapped[str] = mapped_column(
        String, primary_key=True, default=lambda: generate(), init=False
    )
    hub_id: Mapped[str] = mapped_column(
        String, ForeignKey("Hub.id", ondelete="CASCADE"), unique=True, nullable=False
    )
    mod_logs_channel_id: Mapped[str] = mapped_column(String)
    mod_logs_role_id: Mapped[str] = mapped_column(String)
    join_leaves_channel_id: Mapped[str] = mapped_column(String)
    join_leaves_role_id: Mapped[str] = mapped_column(String)
    appeals_channel_id: Mapped[str] = mapped_column(String)
    appeals_role_id: Mapped[str] = mapped_column(String)
    reports_channel_id: Mapped[str] = mapped_column(String)
    reports_role_id: Mapped[str] = mapped_column(String)
    network_alerts_channel_id: Mapped[str] = mapped_column(String)
    network_alerts_role_id: Mapped[str] = mapped_column(String)
    message_moderation_channel_id: Mapped[str] = mapped_column(String)
    message_moderation_role_id: Mapped[str] = mapped_column(String)

    hub: Mapped["Hub"] = relationship("Hub", back_populates="logConfig", lazy="noload")


class HubInvite(Base):
    __tablename__ = "HubInvite"

    code: Mapped[str] = mapped_column(
        String, primary_key=True, default=lambda: generate(size=10), init=False
    )
    hubId: Mapped[str] = mapped_column(
        String, ForeignKey("Hub.id", ondelete="CASCADE"), nullable=False
    )
    expires: Mapped[datetime] = mapped_column(DateTime, nullable=False)

    hub: Mapped["Hub"] = relationship("Hub", back_populates="invites", lazy="noload")

    __table_args__ = (
        Index("ix_hub_invite_hub_id", "hubId"),
        Index("ix_hub_invite_code", "code"),
    )


class HubRulesAcceptance(Base):
    __tablename__ = "HubRulesAcceptance"

    id: Mapped[str] = mapped_column(
        String, primary_key=True, default=lambda: generate(), init=False
    )
    userId: Mapped[str] = mapped_column(String, ForeignKey("User.id"), nullable=False)
    hubId: Mapped[str] = mapped_column(
        String, ForeignKey("Hub.id", ondelete="CASCADE"), nullable=False
    )
    acceptedAt: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())

    user: Mapped["User"] = relationship(
        "User", back_populates="rulesAcceptances", lazy="noload", init=False
    )
    hub: Mapped["Hub"] = relationship(
        "Hub", back_populates="rulesAcceptances", lazy="noload", init=False
    )

    __table_args__ = (
        UniqueConstraint("userId", "hubId"),
        Index("ix_hub_rules_acceptance_hub_user", "hubId", "userId"),
    )


class DonationTierDefinition(Base):
    __tablename__ = "DonationTierDefinition"

    id: Mapped[str] = mapped_column(
        String, primary_key=True, default=lambda: generate(), init=False
    )
    name: Mapped[str] = mapped_column(String, unique=True, nullable=False)
    description: Mapped[str] = mapped_column(String, nullable=False)
    price: Mapped[float] = mapped_column(Float, nullable=False)

    donations: Mapped[list["Donation"]] = relationship(
        "Donation", back_populates="donationTier", lazy="noload"
    )
    users: Mapped[list["User"]] = relationship(
        "User", back_populates="donationTier", lazy="noload"
    )

    createdAt: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())
    updatedAt: Mapped[datetime] = mapped_column(
        DateTime, server_default=func.now(), onupdate=func.now()
    )


class Donation(Base):
    __tablename__ = "Donation"

    id: Mapped[str] = mapped_column(
        String, primary_key=True, default=lambda: generate(), init=False
    )
    kofiTransactionId: Mapped[str] = mapped_column(
        String, unique=True, nullable=False
    )  # Ko-fi transaction ID
    messageId: Mapped[str] = mapped_column(
        String, unique=True, nullable=False
    )  # Ko-fi message ID
    amount: Mapped[float] = mapped_column(Float, nullable=False)  # Donation amount
    currency: Mapped[str] = mapped_column(
        String, nullable=False
    )  # Currency code (e.g., "USD")
    fromName: Mapped[str] = mapped_column(
        String, nullable=False
    )  # Donor name from Ko-fi
    message: Mapped[str] = mapped_column(String)  # Optional donation message
    email: Mapped[str] = mapped_column(String)  # Donor email (if provided)
    kofiTimestamp: Mapped[datetime] = mapped_column(
        DateTime, nullable=False
    )  # Timestamp from Ko-fi
    kofiUrl: Mapped[str] = mapped_column(String)  # Ko-fi donation URL

    # Subscription-specific fields
    donationTierId: Mapped[str] = mapped_column(
        String, ForeignKey("DonationTierDefinition.id")
    )
    discordUserId: Mapped[str] = mapped_column(
        String, ForeignKey("User.id")
    )  # Linked Discord user ID

    discordUser: Mapped["User"] = relationship(
        "User", back_populates="donations", lazy="noload"
    )
    donationTier: Mapped["DonationTierDefinition"] = relationship(
        "DonationTierDefinition", back_populates="donations", lazy="noload"
    )
    createdAt: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())
    updatedAt: Mapped[datetime] = mapped_column(
        DateTime, server_default=func.now(), onupdate=func.now()
    )

    isPublic: Mapped[bool] = mapped_column(
        Boolean, default=True
    )  # Whether donation is public
    processed: Mapped[bool] = mapped_column(
        Boolean, default=False
    )  # Whether premium benefits have been granted

    __table_args__ = (
        Index("ix_donation_kofi_transaction_id", "kofiTransactionId"),
        Index("ix_donation_discord_user_id", "discordUserId"),
        Index("ix_donation_created_at", "createdAt"),
    )


class PendingClaim(Base):
    __tablename__ = "PendingClaim"

    id: Mapped[str] = mapped_column(
        String, primary_key=True, default=lambda: generate(), init=False
    )
    kofiTransactionId: Mapped[str] = mapped_column(
        String, unique=True, nullable=False
    )  # Reference to Ko-fi transaction
    email: Mapped[str] = mapped_column(
        String, nullable=False
    )  # Email from Ko-fi donation
    tierName: Mapped[str] = mapped_column(String)  # Ko-fi tier name
    amount: Mapped[float] = mapped_column(Float, nullable=False)  # Donation amount
    currency: Mapped[str] = mapped_column(String, nullable=False)  # Currency code
    fromName: Mapped[str] = mapped_column(
        String, nullable=False
    )  # Donor name from Ko-fi
    expiresAt: Mapped[datetime] = mapped_column(
        DateTime, nullable=False
    )  # When this claim expires
    claimedBy: Mapped[str] = mapped_column(String)  # Discord user ID who claimed it
    claimedAt: Mapped[datetime] = mapped_column(DateTime)
    createdAt: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())
    updatedAt: Mapped[datetime] = mapped_column(
        DateTime, server_default=func.now(), onupdate=func.now()
    )
    claimed: Mapped[bool] = mapped_column(Boolean, default=False)

    __table_args__ = (
        Index("ix_pending_claim_email", "email"),
        Index("ix_pending_claim_claimed", "claimed"),
        Index("ix_pending_claim_expires_at", "expiresAt"),
        Index("ix_pending_claim_kofi_transaction_id", "kofiTransactionId"),
    )


class HubActivityMetrics(Base):
    __tablename__ = "HubActivityMetrics"

    id: Mapped[str] = mapped_column(
        String, primary_key=True, default=lambda: generate(), init=False
    )
    hubId: Mapped[str] = mapped_column(
        String, ForeignKey("Hub.id", ondelete="CASCADE"), unique=True, nullable=False
    )
    hub: Mapped["Hub"] = relationship(
        "Hub", back_populates="activityMetrics", lazy="noload"
    )
    lastUpdated: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())
    createdAt: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())

    # Daily metrics
    messagesLast24h: Mapped[int] = mapped_column(Integer, default=0)
    activeUsersLast24h: Mapped[int] = mapped_column(Integer, default=0)
    newConnectionsLast24h: Mapped[int] = mapped_column(Integer, default=0)

    # Weekly metrics
    messagesLast7d: Mapped[int] = mapped_column(Integer, default=0)
    activeUsersLast7d: Mapped[int] = mapped_column(Integer, default=0)
    newConnectionsLast7d: Mapped[int] = mapped_column(Integer, default=0)

    # Growth metrics
    memberGrowthRate: Mapped[float] = mapped_column(
        Float, default=0.0
    )  # Percentage growth over 7 days
    engagementRate: Mapped[float] = mapped_column(
        Float, default=0.0
    )  # Messages per active user

    # Timestamps

    __table_args__ = (
        Index("ix_hub_activity_metrics_hub_id", "hubId"),
        Index("ix_hub_activity_metrics_last_updated", "lastUpdated"),
    )


class Announcement(Base):
    __tablename__ = "Announcement"

    id: Mapped[str] = mapped_column(
        String, primary_key=True, default=lambda: generate(), init=False
    )
    title: Mapped[str] = mapped_column(String, nullable=False)
    content: Mapped[str] = mapped_column(String, nullable=False)
    thumbnailUrl: Mapped[str] = mapped_column(String)
    imageUrl: Mapped[str] = mapped_column(String)
    createdAt: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())


class ServerData(Base):
    __tablename__ = "ServerData"

    id: Mapped[str] = mapped_column(String, primary_key=True)
    name: Mapped[str] = mapped_column(String)
    createdAt: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())
    updatedAt: Mapped[datetime] = mapped_column(
        DateTime, server_default=func.now(), onupdate=func.now()
    )

    # Relations
    connections: Mapped[List["Connection"]] = relationship(
        "Connection", back_populates="server", lazy="noload", init=False
    )
    infractions: Mapped[List["Infraction"]] = relationship(
        "Infraction", back_populates="server", lazy="noload", init=False
    )
    serverBans: Mapped[List["ServerBan"]] = relationship(
        "ServerBan", back_populates="server", lazy="noload", init=False
    )
    leaderboardEntries: Mapped[List["LeaderboardEntry"]] = relationship(
        "LeaderboardEntry",
        back_populates="server",
        lazy="noload",
        cascade="all, delete-orphan",
        init=False,
    )

    lastMessageAt: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())
    inviteCode: Mapped[Optional[str]] = mapped_column(
        String, nullable=True, default=None
    )
    messageCount: Mapped[int] = mapped_column(Integer, default=0)
    premiumStatus: Mapped[bool] = mapped_column(Boolean, default=False)
    iconUrl: Mapped[Optional[str]] = mapped_column(String, nullable=True, default=None)


class Call(Base):
    __tablename__ = "Call"

    id: Mapped[str] = mapped_column(
        String, primary_key=True, default=lambda: generate(), init=False
    )
    initiatorId: Mapped[str] = mapped_column(String, nullable=False)
    endTime: Mapped[datetime] = mapped_column(DateTime)
    startTime: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())
    createdAt: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())
    updatedAt: Mapped[datetime] = mapped_column(
        DateTime, server_default=func.now(), onupdate=func.now()
    )

    # Relations
    participants: Mapped[List["CallParticipant"]] = relationship(
        "CallParticipant",
        back_populates="call",
        lazy="noload",
        cascade="all, delete-orphan",
    )
    messages: Mapped[List["CallMessage"]] = relationship(
        "CallMessage",
        back_populates="call",
        lazy="noload",
        cascade="all, delete-orphan",
    )
    ratings: Mapped[List["CallRating"]] = relationship(
        "CallRating", back_populates="call", lazy="noload", cascade="all, delete-orphan"
    )

    status: Mapped[CallStatus] = mapped_column(
        SQLEnum(CallStatus), default=CallStatus.QUEUED
    )

    __table_args__ = (
        Index("ix_call_status", "status"),
        Index("ix_call_start_time", "startTime"),
        Index("ix_call_initiator_id", "initiatorId"),
    )


class CallParticipant(Base):
    __tablename__ = "CallParticipant"

    id: Mapped[str] = mapped_column(
        String, primary_key=True, default=lambda: generate(), init=False
    )
    callId: Mapped[str] = mapped_column(
        String, ForeignKey("Call.id", ondelete="CASCADE"), nullable=False
    )
    call: Mapped["Call"] = relationship(
        "Call", back_populates="participants", lazy="noload"
    )
    users: Mapped[List["CallParticipantUser"]] = relationship(
        "CallParticipantUser",
        back_populates="participant",
        lazy="noload",
        cascade="all, delete-orphan",
    )
    leftAt: Mapped[datetime] = mapped_column(DateTime)
    joinedAt: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())
    channelId: Mapped[str] = mapped_column(String, nullable=False)
    guildId: Mapped[str] = mapped_column(String, nullable=False)
    webhookUrl: Mapped[str] = mapped_column(String, nullable=False)
    messageCount: Mapped[int] = mapped_column(Integer, default=0)

    __table_args__ = (
        UniqueConstraint("callId", "channelId"),
        Index("ix_call_participant_call_id", "callId"),
        Index("ix_call_participant_channel_id", "channelId"),
        Index("ix_call_participant_guild_id", "guildId"),
    )


class CallParticipantUser(Base):
    __tablename__ = "CallParticipantUser"

    id: Mapped[str] = mapped_column(
        String, primary_key=True, default=lambda: generate(), init=False
    )
    leftAt: Mapped[datetime] = mapped_column(DateTime)
    participantId: Mapped[str] = mapped_column(
        String, ForeignKey("CallParticipant.id", ondelete="CASCADE"), nullable=False
    )
    participant: Mapped["CallParticipant"] = relationship(
        "CallParticipant", back_populates="users", lazy="noload"
    )
    userId: Mapped[str] = mapped_column(String, nullable=False)
    joinedAt: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())

    __table_args__ = (
        UniqueConstraint("participantId", "userId"),
        Index("ix_call_participant_user_participant_id", "participantId"),
        Index("ix_call_participant_user_user_id", "userId"),
    )


class CallMessage(Base):
    __tablename__ = "CallMessage"

    id: Mapped[str] = mapped_column(
        String, primary_key=True, default=lambda: generate(), init=False
    )
    callId: Mapped[str] = mapped_column(
        String, ForeignKey("Call.id", ondelete="CASCADE"), nullable=False
    )
    authorId: Mapped[str] = mapped_column(String, nullable=False)
    authorUsername: Mapped[str] = mapped_column(String, nullable=False)
    content: Mapped[str] = mapped_column(String, nullable=False)
    attachmentUrl: Mapped[str] = mapped_column(String)
    call: Mapped["Call"] = relationship(
        "Call", back_populates="messages", lazy="noload"
    )
    timestamp: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())

    __table_args__ = (
        Index("ix_call_message_call_id", "callId"),
        Index("ix_call_message_author_id", "authorId"),
        Index("ix_call_message_timestamp", "timestamp"),
    )


class CallRating(Base):
    __tablename__ = "CallRating"

    id: Mapped[str] = mapped_column(
        String, primary_key=True, default=lambda: generate(), init=False
    )
    callId: Mapped[str] = mapped_column(
        String, ForeignKey("Call.id", ondelete="CASCADE"), nullable=False
    )
    raterId: Mapped[str] = mapped_column(String, ForeignKey("User.id"), nullable=False)
    targetId: Mapped[str] = mapped_column(String, ForeignKey("User.id"), nullable=False)
    rating: Mapped[str] = mapped_column(
        SQLEnum(CallRatingStatus, name="CallRatingStatus", skip_create=True),
        nullable=False,
    )
    call: Mapped["Call"] = relationship("Call", back_populates="ratings", lazy="noload")
    rater: Mapped["User"] = relationship(
        "User", foreign_keys=[raterId], back_populates="ratingsMade", lazy="noload"
    )
    target: Mapped["User"] = relationship(
        "User", foreign_keys=[targetId], back_populates="ratingsReceived", lazy="noload"
    )

    timestamp: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())

    __table_args__ = (
        UniqueConstraint("callId", "raterId", "targetId"),
        Index("ix_call_rating_target_id", "targetId"),
        Index("ix_call_rating_rater_id", "raterId"),
        Index("ix_call_rating_call_id", "callId"),
    )


class ReputationLog(Base):
    __tablename__ = "ReputationLog"

    id: Mapped[str] = mapped_column(
        String, primary_key=True, default=lambda: generate(), init=False
    )
    giverId: Mapped[str] = mapped_column(String, nullable=False)
    receiverId: Mapped[str] = mapped_column(
        String, ForeignKey("User.id"), nullable=False
    )
    reason: Mapped[str] = mapped_column(String, nullable=False)
    timestamp: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())
    receiver: Mapped["User"] = relationship(
        "User", back_populates="reputationLog", lazy="noload"
    )
    automatic: Mapped[bool] = mapped_column(Boolean, default=False)

    __table_args__ = (
        Index("ix_reputation_log_receiver_id", "receiverId"),
        Index("ix_reputation_log_giver_id", "giverId"),
    )


class Account(Base):
    __tablename__ = "Account"

    id: Mapped[str] = mapped_column(
        String, primary_key=True, default=lambda: generate(), init=False
    )
    userId: Mapped[str] = mapped_column(
        String, ForeignKey("User.id", ondelete="CASCADE"), nullable=False
    )
    type: Mapped[str] = mapped_column(String, nullable=False)
    provider: Mapped[str] = mapped_column(String, nullable=False)
    providerAccountId: Mapped[str] = mapped_column(String, nullable=False)
    refreshToken: Mapped[str] = mapped_column(String)
    accessToken: Mapped[str] = mapped_column(String)
    expiresAt: Mapped[int] = mapped_column(Integer)
    tokenType: Mapped[str] = mapped_column(String)
    scope: Mapped[str] = mapped_column(String)
    idToken: Mapped[str] = mapped_column(String)
    sessionState: Mapped[str] = mapped_column(String)

    user: Mapped["User"] = relationship(
        "User", back_populates="accounts", lazy="noload"
    )

    __table_args__ = (UniqueConstraint("provider", "providerAccountId"),)


class Session(Base):
    __tablename__ = "Session"

    id: Mapped[str] = mapped_column(
        String, primary_key=True, default=lambda: generate(), init=False
    )
    sessionToken: Mapped[str] = mapped_column(String, unique=True, nullable=False)
    userId: Mapped[str] = mapped_column(
        String, ForeignKey("User.id", ondelete="CASCADE"), nullable=False
    )
    expires: Mapped[datetime] = mapped_column(DateTime, nullable=False)

    user: Mapped["User"] = relationship(
        "User", back_populates="sessions", lazy="noload"
    )


class Achievement(Base):
    __tablename__ = "Achievement"

    id: Mapped[str] = mapped_column(String, primary_key=True)
    name: Mapped[str] = mapped_column(String, nullable=False)
    description: Mapped[str] = mapped_column(String, nullable=False)
    badgeEmoji: Mapped[str] = mapped_column(String, nullable=False)
    badgeUrl: Mapped[str] = mapped_column(String)

    # Relations
    userAchievements: Mapped[List["UserAchievement"]] = relationship(
        "UserAchievement",
        back_populates="achievement",
        lazy="noload",
        cascade="all, delete-orphan",
    )
    userProgress: Mapped[List["UserAchievementProgress"]] = relationship(
        "UserAchievementProgress",
        back_populates="achievement",
        lazy="noload",
        cascade="all, delete-orphan",
    )

    createdAt: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())
    updatedAt: Mapped[datetime] = mapped_column(
        DateTime, server_default=func.now(), onupdate=func.now()
    )
    threshold: Mapped[int] = mapped_column(Integer, default=1)
    secret: Mapped[bool] = mapped_column(Boolean, default=False)


class UserAchievement(Base):
    __tablename__ = "UserAchievement"

    id: Mapped[str] = mapped_column(
        String, primary_key=True, default=lambda: generate(), init=False
    )
    userId: Mapped[str] = mapped_column(
        String, ForeignKey("User.id", ondelete="CASCADE"), nullable=False
    )
    achievementId: Mapped[str] = mapped_column(
        String, ForeignKey("Achievement.id", ondelete="CASCADE"), nullable=False
    )
    user: Mapped["User"] = relationship(
        "User", back_populates="achievements", lazy="noload"
    )
    achievement: Mapped["Achievement"] = relationship(
        "Achievement", back_populates="userAchievements", lazy="noload"
    )
    unlockedAt: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())

    __table_args__ = (
        UniqueConstraint("userId", "achievementId"),
        Index("ix_user_achievement_userId", "userId"),
        Index("ix_user_achievement_achievementId", "achievementId"),
    )


class UserAchievementProgress(Base):
    __tablename__ = "UserAchievementProgress"

    userId: Mapped[str] = mapped_column(
        String, ForeignKey("User.id", ondelete="CASCADE"), primary_key=True
    )
    achievementId: Mapped[str] = mapped_column(
        String, ForeignKey("Achievement.id", ondelete="CASCADE"), primary_key=True
    )

    user: Mapped["User"] = relationship(
        "User", back_populates="achievementProgress", lazy="noload"
    )
    achievement: Mapped["Achievement"] = relationship(
        "Achievement", back_populates="userProgress", lazy="noload"
    )

    createdAt: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())
    updatedAt: Mapped[datetime] = mapped_column(
        DateTime, server_default=func.now(), onupdate=func.now()
    )
    currentValue: Mapped[int] = mapped_column(Integer, default=0)


class Message(Base):
    __tablename__ = "Message"

    id: Mapped[str] = mapped_column(String, primary_key=True)  # Discord Snowflake
    hub_id: Mapped[str] = mapped_column(ForeignKey("Hub.id", ondelete="CASCADE"))
    content: Mapped[str]
    imageUrl: Mapped[Optional[str]]
    channelId: Mapped[str]
    guildId: Mapped[str]
    authorId: Mapped[str]
    createdAt: Mapped[datetime] = mapped_column(DateTime(timezone=True))
    reactions: Mapped[Optional[dict]] = mapped_column(JSON)
    referredMessageId: Mapped[Optional[str]] = mapped_column(ForeignKey("Message.id"))

    # Relationships
    hub: Mapped["Hub"] = relationship(
        "Hub", back_populates="messages", lazy="noload", init=False
    )

    reports: Mapped[list["Report"]] = relationship(
        "Report",
        back_populates="message",
        lazy="noload",
        cascade="all, delete-orphan",
        init=False,
    )
    broadcasts: Mapped[list["Broadcast"]] = relationship(
        "Broadcast",
        back_populates="message",
        lazy="noload",
        cascade="all, delete-orphan",
        init=False,
    )

    referredTo: Mapped[Optional["Message"]] = relationship(
        "Message",
        remote_side=[id],
        back_populates="referredBy",
        lazy="noload",
        foreign_keys=[referredMessageId],
        init=False,
    )
    referredBy: Mapped[list["Message"]] = relationship(
        "Message",
        back_populates="referredTo",
        lazy="noload",
        foreign_keys=[referredMessageId],
        init=False,
    )


class Broadcast(Base):
    __tablename__ = "Broadcast"

    id: Mapped[str] = mapped_column(
        String, primary_key=True
    )  # Discord message ID of the broadcast message
    messageId: Mapped[str] = mapped_column(
        String, ForeignKey("Message.id", ondelete="CASCADE"), nullable=False
    )
    channelId: Mapped[str] = mapped_column(String, nullable=False)
    message: Mapped["Message"] = relationship(
        "Message", back_populates="broadcasts", lazy="noload", init=False
    )

    createdAt: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())

    __table_args__ = (
        Index("ix_broadcast_message_id", "messageId"),
        Index("ix_broadcast_channel_id", "channelId"),
    )


class Report(Base):
    __tablename__ = "Report"

    id: Mapped[str] = mapped_column(
        String, primary_key=True, default=lambda: generate(size=10), init=False
    )  # Short, unique report ID
    hubId: Mapped[str] = mapped_column(
        String, ForeignKey("Hub.id", ondelete="CASCADE"), nullable=False
    )
    reporterId: Mapped[str] = mapped_column(
        String, ForeignKey("User.id"), nullable=False
    )  # User who submitted the report
    reportedUserId: Mapped[str] = mapped_column(
        String, ForeignKey("User.id"), nullable=False
    )  # User being reported
    reportedServerId: Mapped[str] = mapped_column(
        String, nullable=False
    )  # Server where the reported content originated
    messageId: Mapped[str] = mapped_column(
        String, ForeignKey("Message.id")
    )  # ID of the reported message (if applicable)
    handledBy: Mapped[str] = mapped_column(
        String, ForeignKey("User.id")
    )  # Moderator who handled the report
    handledAt: Mapped[datetime] = mapped_column(DateTime)  # When the report was handled
    reason: Mapped[str] = mapped_column(String, nullable=False)  # Reason for the report

    hub: Mapped["Hub"] = relationship("Hub", back_populates="reports", lazy="noload")
    reporter: Mapped["User"] = relationship(
        "User",
        foreign_keys=[reporterId],
        back_populates="reportsSubmitted",
        lazy="noload",
    )
    reportedUser: Mapped["User"] = relationship(
        "User",
        foreign_keys=[reportedUserId],
        back_populates="reportsReceived",
        lazy="noload",
    )
    handler: Mapped["User"] = relationship(
        "User", foreign_keys=[handledBy], back_populates="reportsHandled", lazy="noload"
    )
    message: Mapped["Message"] = relationship(
        "Message", back_populates="reports", lazy="noload"
    )

    createdAt: Mapped[datetime] = mapped_column(DateTime, server_default=func.now())
    updatedAt: Mapped[datetime] = mapped_column(
        DateTime, server_default=func.now(), onupdate=func.now()
    )
    status: Mapped[ReportStatus] = mapped_column(
        SQLEnum(ReportStatus), default=ReportStatus.PENDING
    )

    __table_args__ = (
        Index("ix_report_hub_id", "hubId"),
        Index("ix_report_status", "status"),
        Index("ix_report_created_at", "createdAt"),
        Index("ix_report_reporter_id", "reporterId"),
        Index("ix_report_message_id", "messageId"),
        Index("ix_report_handled_by", "handledBy"),
        Index("ix_report_reported_user_id", "reportedUserId"),
    )


class Ban(Base):
    __tablename__ = "Ban"

    id: Mapped[str] = mapped_column(
        String, primary_key=True, default=lambda: generate(size=10), init=False
    )
    userId: Mapped[str] = mapped_column(
        String, ForeignKey("User.id"), nullable=False
    )  # User being banned
    moderatorId: Mapped[str] = mapped_column(
        String, ForeignKey("User.id"), nullable=False
    )  # Staff member who issued the ban
    reason: Mapped[str] = mapped_column(String, nullable=False)  # Reason for the ban
    duration: Mapped[int | None] = mapped_column(
        Integer, nullable=True, default=None
    )  # Duration in milliseconds for temporary bans (null for permanent)
    expiresAt: Mapped[DateTime | None] = mapped_column(
        DateTime, nullable=True, default=None
    )  # When the ban expires (null for permanent bans)

    user: Mapped["User"] = relationship(
        "User", foreign_keys=[userId], back_populates="bans", lazy="noload", init=False
    )
    moderator: Mapped["User"] = relationship(
        "User",
        foreign_keys=[moderatorId],
        back_populates="issuedBans",
        lazy="noload",
        init=False,
    )

    createdAt: Mapped[Optional[DateTime]] = mapped_column(
        DateTime, server_default=func.now(), default=None
    )
    updatedAt: Mapped[Optional[DateTime]] = mapped_column(
        DateTime, server_default=func.now(), onupdate=func.now(), default=None
    )
    type: Mapped[BanType] = mapped_column(
        SQLEnum(BanType, name="BanType", skip_create=True), default=BanType.PERMANENT
    )
    status: Mapped[BanStatus] = mapped_column(
        SQLEnum(BanStatus), default=BanStatus.ACTIVE
    )

    __table_args__ = (
        Index("ix_ban_user_id", "userId"),
        Index("ix_ban_status", "status"),
        Index("ix_ban_expires_at", "expiresAt"),
        Index("ix_ban_created_at", "createdAt"),
    )


class ServerBan(Base):
    __tablename__ = "ServerBan"

    id: Mapped[str] = mapped_column(
        String, primary_key=True, default=lambda: generate(size=10), init=False
    )
    serverId: Mapped[str] = mapped_column(
        String, ForeignKey("ServerData.id"), nullable=False
    )  # Discord server ID being banned
    moderatorId: Mapped[str] = mapped_column(
        String, ForeignKey("User.id"), nullable=False
    )  # Staff member who issued the ban
    reason: Mapped[str] = mapped_column(String, nullable=False)  # Reason for the ban
    duration: Mapped[int] = mapped_column(
        Integer
    )  # Duration in milliseconds for temporary bans (null for permanent)
    expiresAt: Mapped[DateTime | None] = mapped_column(
        DateTime, nullable=True, default=None
    )  # When the ban expires (null for permanent bans)

    server: Mapped["ServerData"] = relationship(
        "ServerData", back_populates="serverBans", lazy="noload", init=False
    )
    moderator: Mapped["User"] = relationship(
        "User", back_populates="issuedServerBans", lazy="noload", init=False
    )

    createdAt: Mapped[DateTime] = mapped_column(
        DateTime, server_default=func.now(), default=None
    )
    updatedAt: Mapped[DateTime] = mapped_column(
        DateTime, server_default=func.now(), onupdate=func.now(), default=None
    )
    type: Mapped[BanType] = mapped_column(
        SQLEnum(BanType, name="BanType", skip_create=True), default=BanType.PERMANENT
    )
    status: Mapped[BanStatus] = mapped_column(
        SQLEnum(BanStatus), default=BanStatus.ACTIVE
    )

    __table_args__ = (
        Index("ix_server_ban_server_id", "serverId"),
        Index("ix_server_ban_status", "status"),
        Index("ix_server_ban_expires_at", "expiresAt"),
        Index("ix_server_ban_created_at", "createdAt"),
    )


class ModerationLog(Base):
    __tablename__ = "ModerationLog"

    id: Mapped[str] = mapped_column(
        String, primary_key=True, default=lambda: generate(), init=False
    )
    hubId: Mapped[str] = mapped_column(
        String, ForeignKey("Hub.id", ondelete="CASCADE"), nullable=False
    )
    moderatorId: Mapped[str] = mapped_column(
        String, ForeignKey("User.id"), nullable=False
    )
    action: Mapped[str] = mapped_column(
        String, nullable=False
    )  # e.g., "WARN", "MUTE", "BAN"
    targetId: Mapped[str] = mapped_column(String, nullable=False)  # User or server ID
    reason: Mapped[str] = mapped_column(String, nullable=False)

    hub: Mapped["Hub"] = relationship(
        "Hub", back_populates="moderationLogs", lazy="noload"
    )
    moderator: Mapped["User"] = relationship(
        "User", back_populates="moderationLogs", lazy="noload"
    )

    timestamp: Mapped[DateTime] = mapped_column(DateTime, server_default=func.now())

    __table_args__ = (
        Index("ix_moderation_log_hub_id", "hubId"),
        Index("ix_moderation_log_moderator_id", "moderatorId"),
        Index("ix_moderation_log_action", "action"),
    )


class LeaderboardEntry(Base):
    __tablename__ = "LeaderboardEntry"

    id: Mapped[str] = mapped_column(
        String, primary_key=True, default=lambda: generate(), init=False
    )
    userId: Mapped[str] = mapped_column(
        String, ForeignKey("User.id", ondelete="CASCADE"), nullable=False
    )
    hubId: Mapped[str] = mapped_column(String, ForeignKey("Hub.id", ondelete="CASCADE"))
    serverId: Mapped[str] = mapped_column(
        String, ForeignKey("ServerData.id", ondelete="CASCADE")
    )
    period: Mapped[LeaderboardPeriod] = mapped_column(
        SQLEnum(LeaderboardPeriod), nullable=False
    )
    type: Mapped[LeaderboardType] = mapped_column(
        SQLEnum(LeaderboardType), nullable=False
    )

    user: Mapped["User"] = relationship(
        "User", back_populates="leaderboardEntries", lazy="noload"
    )
    hub: Mapped["Hub"] = relationship(
        "Hub", back_populates="leaderboardEntries", lazy="noload"
    )
    server: Mapped["ServerData"] = relationship(
        "ServerData", back_populates="leaderboardEntries", lazy="noload"
    )

    lastActivityAt: Mapped[DateTime] = mapped_column(
        DateTime, server_default=func.now()
    )
    createdAt: Mapped[DateTime] = mapped_column(DateTime, server_default=func.now())
    updatedAt: Mapped[DateTime] = mapped_column(
        DateTime, server_default=func.now(), onupdate=func.now()
    )
    messageCount: Mapped[int] = mapped_column(Integer, default=0)
    score: Mapped[int] = mapped_column(Integer, default=0)
    rank: Mapped[int] = mapped_column(Integer, default=0)

    __table_args__ = (
        Index("ix_leaderboard_entry_type_period_score", "type", "period", "score"),
        Index("ix_leaderboard_entry_user_type_period", "userId", "type", "period"),
        Index("ix_leaderboard_entry_hub_type_period", "hubId", "type", "period"),
        Index("ix_leaderboard_entry_server_type_period", "serverId", "type", "period"),
        Index("ix_leaderboard_entry_last_activity_at", "lastActivityAt"),
    )


class User(Base):
    __tablename__ = "User"

    # Primary fields
    id: Mapped[str] = mapped_column(String, primary_key=True)
    name: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    image: Mapped[Optional[str]] = mapped_column(String, nullable=True)

    # Relations - ALL with init=False to exclude from constructor
    ownedHubs: Mapped[list["Hub"]] = relationship(
        "Hub", back_populates="owner", lazy="noload", init=False
    )
    appeals: Mapped[list["Appeal"]] = relationship(
        "Appeal", back_populates="user", lazy="noload", init=False
    )
    infractions: Mapped[list["Infraction"]] = relationship(
        "Infraction",
        foreign_keys="Infraction.userId",
        back_populates="user",
        lazy="noload",
        init=False,
    )
    issuedInfractions: Mapped[list["Infraction"]] = relationship(
        "Infraction",
        foreign_keys="Infraction.moderatorId",
        back_populates="moderator",
        lazy="noload",
        init=False,
    )
    upvotedHubs: Mapped[list["HubUpvote"]] = relationship(
        "HubUpvote", back_populates="user", lazy="noload", init=False
    )
    reputationLog: Mapped[list["ReputationLog"]] = relationship(
        "ReputationLog", back_populates="receiver", lazy="noload", init=False
    )
    modPositions: Mapped[list["HubModerator"]] = relationship(
        "HubModerator", back_populates="user", lazy="noload", init=False
    )
    reviews: Mapped[list["HubReview"]] = relationship(
        "HubReview",
        back_populates="user",
        lazy="noload",
        cascade="all, delete-orphan",
        init=False,
    )
    blockWordsCreated: Mapped[list["BlockWord"]] = relationship(
        "BlockWord", back_populates="creator", lazy="noload", init=False
    )
    antiSwearRulesCreated: Mapped[list["AntiSwearRule"]] = relationship(
        "AntiSwearRule", back_populates="creator", lazy="noload", init=False
    )
    rulesAcceptances: Mapped[list["HubRulesAcceptance"]] = relationship(
        "HubRulesAcceptance", back_populates="user", lazy="noload", init=False
    )
    ratingsMade: Mapped[list["CallRating"]] = relationship(
        "CallRating",
        foreign_keys="CallRating.raterId",
        back_populates="rater",
        lazy="noload",
        init=False,
    )
    ratingsReceived: Mapped[list["CallRating"]] = relationship(
        "CallRating",
        foreign_keys="CallRating.targetId",
        back_populates="target",
        lazy="noload",
        init=False,
    )
    accounts: Mapped[list["Account"]] = relationship(
        "Account",
        back_populates="user",
        lazy="noload",
        cascade="all, delete-orphan",
        init=False,
    )
    sessions: Mapped[list["Session"]] = relationship(
        "Session",
        back_populates="user",
        lazy="noload",
        cascade="all, delete-orphan",
        init=False,
    )
    reportsSubmitted: Mapped[list["Report"]] = relationship(
        "Report",
        foreign_keys="Report.reporterId",
        back_populates="reporter",
        lazy="noload",
        init=False,
    )
    reportsReceived: Mapped[list["Report"]] = relationship(
        "Report",
        foreign_keys="Report.reportedUserId",
        back_populates="reportedUser",
        lazy="noload",
        init=False,
    )
    reportsHandled: Mapped[list["Report"]] = relationship(
        "Report",
        foreign_keys="Report.handledBy",
        back_populates="handler",
        lazy="noload",
        init=False,
    )
    achievements: Mapped[list["UserAchievement"]] = relationship(
        "UserAchievement",
        back_populates="user",
        lazy="noload",
        cascade="all, delete-orphan",
        init=False,
    )
    achievementProgress: Mapped[list["UserAchievementProgress"]] = relationship(
        "UserAchievementProgress",
        back_populates="user",
        lazy="noload",
        cascade="all, delete-orphan",
        init=False,
    )
    bans: Mapped[list["Ban"]] = relationship(
        "Ban",
        foreign_keys="Ban.userId",
        back_populates="user",
        lazy="noload",
        init=False,
    )
    issuedBans: Mapped[list["Ban"]] = relationship(
        "Ban",
        foreign_keys="Ban.moderatorId",
        back_populates="moderator",
        lazy="noload",
        init=False,
    )
    issuedServerBans: Mapped[list["ServerBan"]] = relationship(
        "ServerBan", back_populates="moderator", lazy="noload", init=False
    )
    leaderboardEntries: Mapped[list["LeaderboardEntry"]] = relationship(
        "LeaderboardEntry",
        back_populates="user",
        lazy="noload",
        cascade="all, delete-orphan",
        init=False,
    )
    donations: Mapped[list["Donation"]] = relationship(
        "Donation", back_populates="discordUser", lazy="noload", init=False
    )
    donationTier: Mapped["DonationTierDefinition | None"] = relationship(
        "DonationTierDefinition", back_populates="users", lazy="noload", init=False
    )
    moderationLogs: Mapped[list["ModerationLog"]] = relationship(
        "ModerationLog", back_populates="moderator", lazy="noload", init=False
    )

    # Timestamps
    lastMessageAt: Mapped[Optional[datetime]] = mapped_column(
        DateTime, server_default=func.now()
    )
    inboxLastReadDate: Mapped[Optional[datetime]] = mapped_column(
        DateTime, server_default=func.now()
    )
    createdAt: Mapped[Optional[datetime]] = mapped_column(
        DateTime, server_default=func.now()
    )
    updatedAt: Mapped[Optional[datetime]] = mapped_column(
        DateTime, server_default=func.now(), onupdate=func.now()
    )

    # Foreign keys
    donationTierId: Mapped[Optional[str]] = mapped_column(
        String, ForeignKey("DonationTierDefinition.id"), nullable=True, default=None
    )
    tagId: Mapped[Optional[str]] = mapped_column(String, nullable=True, default=None)

    # Optional enum field
    activityLevel: Mapped[Optional[HubActivityLevel]] = mapped_column(
        SQLEnum(HubActivityLevel, name="HubActivityLevel", skip_create=True),
        default=None,
        nullable=True,
    )  # user's preferred activity level

    # Activity tracking for recommendations
    donationExpiresAt: Mapped[Optional[datetime]] = mapped_column(
        DateTime, nullable=True, default=None
    )  # When the user's donation expires
    lastHubJoinAt: Mapped[Optional[datetime]] = mapped_column(
        DateTime, nullable=True, default=None
    )  # Last time the user joined a hub

    # nextauth
    email: Mapped[Optional[str]] = mapped_column(String, nullable=True, default=None)
    emailVerified: Mapped[Optional[datetime]] = mapped_column(
        DateTime, nullable=True, default=None
    )
    donationEmail: Mapped[Optional[str]] = mapped_column(
        String, nullable=True, default=None
    )  # Email for donation matching

    # Boolean flags
    showBadges: Mapped[bool] = mapped_column(Boolean, default=True)
    mentionOnReply: Mapped[bool] = mapped_column(Boolean, default=True)
    isStaff: Mapped[bool] = mapped_column(
        Boolean, default=False
    )  # Whether this user is staff
    showNsfwHubs: Mapped[bool] = mapped_column(
        Boolean, default=False
    )  # Whether user wants to see NSFW hubs

    # Numeric fields
    voteCount: Mapped[int] = mapped_column(Integer, default=0)
    reputation: Mapped[int] = mapped_column(Integer, default=0)
    messageCount: Mapped[int] = mapped_column(Integer, default=0)
    hubJoinCount: Mapped[int] = mapped_column(Integer, default=0)
    hubEngagementScore: Mapped[float] = mapped_column(
        Float, default=0.0
    )  # Calculated engagement metric

    locale: Mapped[Optional[str]] = mapped_column(String, nullable=True, default="en")
    lastVoted: Mapped[Optional[datetime]] = mapped_column(
        DateTime, nullable=True, default=None
    )

    badges: Mapped[list[Badges]] = mapped_column(
        ARRAY(SQLEnum(Badges, name="Badges", create_type=False)), default=list
    )

    # Hub recommendation preferences
    preferredLanguages: Mapped[list[str]] = mapped_column(ARRAY(String), default=list)

    # Indexes
    __table_args__ = (
        Index("ix_user_is_staff", "isStaff"),
        Index("ix_user_reputation", "reputation"),
        Index("ix_user_locale", "locale"),
        Index("ix_user_email", "email"),
        Index("ix_user_vote_count", "voteCount"),
        Index("ix_user_last_voted", "lastVoted"),
        Index("ix_user_created_at", "createdAt"),
    )
