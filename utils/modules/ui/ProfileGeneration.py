from io import <PERSON><PERSON><PERSON>
from typing import TYPE_CHECKING
import discord

from jinja2 import Template
from playwright.async_api import async_playwright
import asyncio

from utils.utils import load_profile_data, abbreviate_number
from data.profile import profile_template

if TYPE_CHECKING:
    from main import Bot

_browser = None
_browser_lock = asyncio.Lock()


async def get_browser():
    global _browser
    async with _browser_lock:
        if _browser is None or not _browser.is_connected():
            playwright = await async_playwright().start()
            _browser = await playwright.chromium.launch(
                headless=True,
                args=[
                    "--no-sandbox",
                    "--disable-dev-shm-usage",
                    "--disable-extensions",
                    "--disable-background-timer-throttling",
                    "--disable-backgrounding-occluded-windows",
                    "--disable-renderer-backgrounding",
                ],
            )
        return _browser


async def capture_html_screenshot(
    html_content: str, width: int = 800, height: int = 600
) -> BytesIO:
    browser = await get_browser()

    context = await browser.new_context(
        viewport={"width": width, "height": height}, device_scale_factor=1
    )

    buffer = BytesIO()

    try:
        page = await context.new_page()
        await page.set_content(html_content, wait_until="networkidle")

        try:
            await page.wait_for_selector(".profile-card", timeout=3000)
            profile_card = await page.query_selector(".profile-card")
            ss_buffer = await profile_card.screenshot(type="png")
            buffer.write(ss_buffer)
        except Exception:
            ss_buffer = await page.screenshot(type="png", full_page=True)
            buffer.write(ss_buffer)

        buffer.seek(0)
        return buffer
    finally:
        await context.close()


async def generate_profile(
    bot: "Bot",
    interaction: discord.Interaction,
    achievements: list[str],
    badges: list[str],
    user: discord.User,
) -> BytesIO:
    template = Template(profile_template)
    username = user.display_name or user.name

    words = username.split()
    if len(words) >= 2:
        initials = words[0][0].upper() + words[1][0].upper()
    else:
        initials = username[0].upper() if username else "?"

    avatar_url = user.avatar.url if user.avatar else None
    loaded_data = await load_profile_data(bot, user)

    profile_data = {
        "username": f"@{user.name}",
        "guild_tag": (
            f"#{interaction.guild.name}" if interaction.guild else "#Direct Message"
        ),
        "avatar_url": avatar_url,
        "avatar_initials": initials,
        "message_count": abbreviate_number(loaded_data.messageCount),
        "global_rank": "#1",
        "reputation": loaded_data.reputation,
    }

    rendered_html = template.render(
        profile=profile_data, achievements=achievements, badges=badges
    )

    return await capture_html_screenshot(rendered_html, width=1180, height=800)


async def cleanup_browser():
    global _browser
    if _browser:
        await _browser.close()
        _browser = None
