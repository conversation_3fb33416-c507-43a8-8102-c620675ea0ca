import discord
from discord.ext import commands

from jinja2 import Template
from playwright.async_api import async_playwright
import os
import re
from datetime import datetime, timedelta
from sqlalchemy import select, desc
from sqlalchemy.orm import selectinload
from typing import TYPE_CHECKING, Optional, Union

from utils.modules.core.db.models import User, Achievement, UserAchievement
from utils.modules.core.checks import is_interchat_staff

from data.profile import profile_template
from data.badges import badges

if TYPE_CHECKING:
    from main import Bot
    from utils.constants import InterchatConstants


discordInteraction = Union[commands.Context, discord.Interaction]


def get_source_user(source: discordInteraction):
    return source.user if isinstance(source, discord.Interaction) else source.author


def get_source_bot(source: discordInteraction):
    return source.client if isinstance(source, discord.Interaction) else source.bot


async def chunk_guilds(guilds):
    for guild in sorted(guilds, key=lambda g: g.member_count, reverse=True):
        if guild.chunked is False:
            await guild.chunk(cache=True)


def parse_discord_emoji(emoji_input: discord.Emoji):
    if not emoji_input:
        return "❓"

    if hasattr(emoji_input, "id") and hasattr(emoji_input, "name"):
        emoji_id = emoji_input.id
        name = emoji_input.name
        animated = getattr(emoji_input, "animated", False)
        extension = "gif" if animated else "png"
        emoji_url = f"https://cdn.discordapp.com/emojis/{emoji_id}.{extension}"
        return f'<img src="{emoji_url}" alt="{name}" class="custom-emoji">'

    if isinstance(emoji_input, str):
        custom_emoji_pattern = r"<(a?):([^:]+):(\d+)>"
        match = re.match(custom_emoji_pattern, emoji_input)
        if match:
            animated, name, emoji_id = match.groups()
            extension = "gif" if animated else "png"
            emoji_url = f"https://cdn.discordapp.com/emojis/{emoji_id}.{extension}"
            return f'<img src="{emoji_url}" alt="{name}" class="custom-emoji">'

        return emoji_input

    return "❓"


async def fetch_badges(
    bot: "Bot",
    interaction: discord.Interaction,
    constants: "InterchatConstants",
    user: discord.User,
):
    badges_list: list[dict[str, str]] = []

    if user.id in constants.auth_users():
        badges_list.append(
            {
                "icon": parse_discord_emoji(bot.emotes.developer_badge),
                "title": "InterChat Developer",
                "description": "Core developer of InterChat",
            }
        )

    if await is_interchat_staff(interaction, user=user):
        badges_list.append(
            {
                "icon": parse_discord_emoji(bot.emotes.staff_badge),
                "title": "InterChat Staff",
                "description": "InterChat staff member",
            }
        )

    async with bot.db.get_session() as session:
        stmt = select(User.badges).where(User.id == str(user.id))
        res = (await session.execute(stmt)).scalar()

    if res:
        for badge in res:
            badge_key = badge.value
            badge_info = badges[badge_key]
            icon_attr = getattr(bot.emotes, badge_info["icon"], None)
            if icon_attr:
                data = {
                    "icon": parse_discord_emoji(icon_attr),
                    "title": badge_info["name"],
                    "description": badge_info["description"],
                }
                badges_list.append(data)

    return badges_list


async def fetch_achievements(
    bot: "Bot",
    interaction: discord.Interaction | commands.Context["Bot"],
    user: discord.User,
    limit: int = 6,
):
    achievement_list: list[dict[str, str]] = []

    async with bot.db.get_session() as session:
        stmt = (
            select(UserAchievement)
            .where(UserAchievement.userId == str(user.id))
            .options(selectinload(UserAchievement.achievement))
            .order_by(desc(UserAchievement.unlockedAt))
            .limit(limit)
        )
        result = (await session.execute(stmt)).scalars().all()

        for ua in result:
            achievement = ua.achievement
            if not achievement:
                continue

            data = {
                "icon": parse_discord_emoji(achievement.badgeEmoji),
                "title": achievement.name,
                "description": achievement.description,
            }
            achievement_list.append(data)

    return achievement_list


async def load_profile_data(bot: "Bot", user: discord.User):
    async with bot.db.get_session() as session:
        stmt = select(User).where(User.id == str(user.id))
        res = (await session.execute(stmt)).scalar_one_or_none()

    return res


async def load_user_locale(bot: "Bot", source: discordInteraction):
    user = get_source_user(source)
    async with bot.db.get_session() as session:
        stmt = select(User.locale).where(User.id == str(user.id))
        res = (await session.execute(stmt)).scalar()
    return res


def check_user():
    async def predicate(ctx: commands.Context["Bot"]) -> bool:
        await ctx.defer()
        async with ctx.bot.db.get_session() as session:
            stmt = select(User.id).where(User.id == str(ctx.author.id))
            existing = (await session.execute(stmt)).scalar()

            if not existing:
                user = User(
                    id=str(ctx.author.id),
                    name=ctx.author.name,
                    image=ctx.author.display_avatar.url,
                    locale="en",
                    badges=[],
                    preferredLanguages=[],
                    lastMessageAt=datetime.now(),
                    inboxLastReadDate=datetime.now(),
                    createdAt=datetime.now(),
                    updatedAt=datetime.now(),
                )
                session.add(user)
                await session.commit()

        return True

    return commands.check(predicate)


def abbreviate_number(n):
    if n >= 1_000_000_000:
        return f"{n/1_000_000_000:.1f}B"
    elif n >= 1_000_000:
        return f"{n/1_000_000:.1f}M"
    elif n >= 1_000:
        return f"{n/1_000:.1f}k"
    else:
        return str(n)


def parse_duration(duration_str: str) -> Optional[int]:
    """Parse duration string like '1d', '2w', '3m', '1y' into milliseconds."""
    if not duration_str or duration_str.lower() in ["permanent", "perm", ""]:
        return None

    # Match pattern like '1d', '2w', etc.
    match = re.match(r"^(\d+)([mdwMy])$", duration_str.lower().strip())
    if not match:
        return None

    amount, unit = match.groups()
    amount = int(amount)

    if unit == "m":  # minutes
        return amount * 60 * 1000
    elif unit == "d":  # days
        return amount * 24 * 60 * 60 * 1000
    elif unit == "w":  # weeks
        return amount * 7 * 24 * 60 * 60 * 1000
    elif unit == "M":  # months (30 days)
        return amount * 30 * 24 * 60 * 60 * 1000
    elif unit == "y":  # years (365 days)
        return amount * 365 * 24 * 60 * 60 * 1000

    return None


def duration_to_datetime(duration_ms: Optional[int]) -> Optional[datetime]:
    if duration_ms is None:
        return None
    return datetime.now() + timedelta(milliseconds=duration_ms)
