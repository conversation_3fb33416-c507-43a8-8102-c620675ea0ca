# InterChat.py Copilot Instructions

## Project Overview
InterChat is a Discord bot that enables cross-server message broadcasting through "hubs". Messages sent in connected channels are automatically relayed to all other channels within the same hub using webhooks.

## Core Architecture

### Database-First Design
- Uses SQLAlchemy async ORM with PostgreSQL
- Models in `utils/modules/core/db/models.py` define the schema (1475+ lines of models)
- Key entities: `Hub`, `Connection`, `ServerData`, user infractions
- Database session management through async context managers: `async with self.bot.db.get_session() as session:`

### Message Broadcasting System
The core feature lives in `cogs/events/on_message.py`:
1. Check if message channel is connected to a hub
2. Query all other connections in the same hub
3. Broadcast via webhooks with original author's name/avatar
4. Support for both regular channels and forum threads (`parentId` field)

### Bot Structure
- `main.py`: AutoShardedBot with cogwatch hot-reloading
- Cogs organized by purpose: `events/`, `modules/`, `public/`, `staff/`, `developer/`
- Constants pattern: `InterchatConstants()` class for environment variables
- Custom emoji management through `EmojiManager` that loads application emojis

## Development Patterns

### Environment Configuration
Always use `InterchatConstants()` for config, never direct `os.getenv()`:
```python
constants = InterchatConstants()
token = constants.token()  # Not os.getenv('TOKEN')
```

### Database Operations
Pattern for database interactions:
```python
async with self.bot.db.get_session() as session:
    stmt = select(Hub).where(Hub.id == hub_id)
    result = await session.execute(stmt)
    hub = result.scalar_one_or_none()
```

### Command Structure
- Hybrid commands (slash + prefix): `@commands.hybrid_command()`
- Custom decorators: `@check_user()` for user validation
- Extras metadata: `extras={"category": "Hubs"}` for help organization

### Error Handling & Monitoring
- Sentry integration in production
- Custom error handling in `utils/modules/errors/`
- Comprehensive logging with colorlog formatting
- Rate limiting through Redis (`utils/modules/core/ratelimit.py`)

## Key Dependencies & Tools
- `discord.py` 2.5.2 with AutoShardedBot
- `cogwatch` for hot-reloading during development
- `jishaku` for developer commands (don't remove)
- `asyncpg` + `sqlalchemy` for async database operations
- `redis` for rate limiting and caching
- `playwright` for web scraping/rendering features

## Development Workflow
1. Use `docker-compose up` for local development
2. Database tables auto-create on startup via `await self.db.create_tables()`
3. Cogs auto-load from directory traversal in `setup_hook()`
4. Hot-reload enabled with `@watch(path='cogs')` decorator

## Critical Files to Understand
- `main.py`: Bot lifecycle, database init, cog loading
- `utils/modules/core/db/models.py`: Complete data model
- `cogs/events/on_message.py`: Core message broadcasting logic
- `utils/constants.py`: Configuration and logging setup
- `cogs/modules/hub.py`: Hub management commands

## Testing & Debugging
- Set `ENVIRONMENT=development` for dev mode features
- Database echo can be enabled in `database.py` for SQL debugging
- Check for type hints and linting issues, but don't let them block your workflow

