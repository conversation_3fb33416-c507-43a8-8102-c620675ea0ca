import discord
from discord.ext import commands
from discord.ui import View, button, <PERSON><PERSON>, Select, select as discord_select, TextInput

from sqlalchemy import DateTime, select
from datetime import datetime, timedelta
from typing import Optional

from utils.modules.core.db.models import (
    Infraction,
    Connection,
    InfractionType,
    InfractionStatus,
    Ban,
    ServerBan,
    BanType,
    BanStatus,
    Hub,
)
from utils.modules.ui.CreateEmbed import create_embed, get_text
from utils.modules.ui.CustomModal import CustomModal
from utils.modules.ui.ModerationViews import TargetSelectionView, HubSelectionView
from utils.modules.core.checks import is_interchat_staff, interaction_check
from utils.modules.core.moderation_utils import (
    get_user_moderated_hubs,
    fetch_original_message,
)
from utils.constants import InterchatConstants, logger
import re

from utils.utils import duration_to_datetime, parse_duration


class Paginator(View):
    def __init__(self, bot, user, target_user, constants, locale, logs):
        super().__init__(timeout=300)
        self.bot = bot
        self.user = user
        self.target_user = target_user
        self.constants = constants
        self.locale = locale
        self.logs = logs
        self.current = 0
        self.message = None

        self.update_button_states()

    def fetch_message(self, message):
        self.message = message

    def update_button_states(self):
        if len(self.logs) == 0:
            self.jump_callback.disabled = True
            self.left_callback.disabled = True
            self.right_callback.disabled = True
            self.search_callback.disabled = True
        else:
            self.left_callback.disabled = self.current == 0
            self.right_callback.disabled = self.current == len(self.logs) - 1

        self.jump_callback.emoji = self.bot.emotes.hash_icon
        self.left_callback.emoji = self.bot.emotes.arrow_left
        self.right_callback.emoji = self.bot.emotes.arrow_right
        self.search_callback.emoji = self.bot.emotes.search_icon

    def get_embed(self) -> discord.Embed:
        if len(self.logs) == 0:
            return discord.Embed(
                title="Error!",
                description=f"{self.bot.emotes.x_icon} No logs found for this user.",
                color=discord.Color.red(),
            )

        logs = self.logs[self.current]
        embed = discord.Embed(
            title="Moderation History", description=" ", color=self.constants.color()
        )
        embed.set_author(
            name=f"@{self.target_user.name}",
            icon_url=self.target_user.display_avatar.url,
        )
        embed.add_field(
            name=logs.get("type", "N/A"), value=logs.get("date", "N/A"), inline=False
        )
        return embed

    @discord_select(
        placeholder="Select an action",
        options=[
            discord.SelectOption(label="Loading..", value="NONE")  # Placeholder option
        ],
        max_values=1,
        min_values=1,
    )
    async def on_submit(self, interaction: discord.Interaction, select: Select): ...

    @button(emoji="⚙️", style=discord.ButtonStyle.grey)
    async def jump_callback(self, interaction: discord.Interaction, button: Button):
        await interaction_check(interaction, self.user, interaction.user)
        ...  # Implement jump functionality here

    @button(emoji="◀️", style=discord.ButtonStyle.grey)
    async def left_callback(self, interaction: discord.Interaction, button: Button):
        await interaction_check(interaction, self.user, interaction.user)
        if self.current > 0:
            self.current -= 1
            self.update_button_states()
            embed = self.get_embed()
            await interaction.response.edit_message(embed=embed, view=self)

    @button(emoji="▶️", style=discord.ButtonStyle.grey)
    async def right_callback(self, interaction: discord.Interaction, button: Button):
        await interaction_check(interaction, self.user, interaction.user)
        if self.current < len(self.logs) - 1:
            self.current += 1
            self.update_button_states()
            embed = self.get_embed()
            await interaction.response.edit_message(embed=embed, view=self)

    @button(emoji="📖", style=discord.ButtonStyle.grey)
    async def search_callback(self, interaction: discord.Interaction, button: Button):
        await interaction_check(interaction, self.user, interaction.user)
        ...  # Implement search functionality here


class ModerationButtons(View):
    def __init__(
        self,
        bot,
        user,
        constants,
        locale,
        target_user,
        target_mesage,
        channel_id: str,
        target_server: Optional[discord.Guild] = None,
        selected_hub: "Optional[Hub]" = None,
    ):
        super().__init__(timeout=300)
        self.bot = bot
        self.user = user
        self.constants = constants
        self.locale = locale
        self.target_user = target_user
        self.target_message = target_mesage
        self.channel_id = channel_id
        self.target_server = target_server
        self.selected_hub = selected_hub

    async def get_hub_id_from_channel(self) -> Optional[str]:
        """Get the hub ID associated with the current channel."""
        try:
            async with self.bot.db.get_session() as session:
                stmt = select(Connection).where(Connection.channelId == self.channel_id)
                connection = (await session.execute(stmt)).scalar_one_or_none()
                return connection.hubId if connection else None
        except Exception as error:
            logger.error(
                f"Error getting hub ID from channel {self.channel_id}: {error}"
            )
            return None

    async def create_infraction_modal(self, interaction: discord.Interaction) -> None:
        """Create and show a modal for collecting infraction reason."""
        modal = CustomModal(
            title="Warn User",
            options=[
                (
                    "reason",
                    TextInput(
                        label="Reason",
                        placeholder="Enter the reason for warning this user...",
                        style=discord.TextStyle.paragraph,
                        max_length=500,
                        required=True,
                    ),
                ),
            ],
            callback=self.handle_warn_submission,
        )
        await interaction.response.send_modal(modal)

    async def handle_warn_submission(
        self, modal: CustomModal, interaction: discord.Interaction
    ) -> None:
        """Handle the submission of the warn modal."""
        hub_id = await self.get_hub_id_from_channel()
        if not hub_id:
            await interaction.followup.send(
                f"{self.bot.emotes.x_icon} This channel is not connected to any hub.",
                ephemeral=True,
            )
            return

        reason = modal.reason.value

        print(self.target_user, self.target_server)

        # Create the infraction record
        async with self.bot.db.get_session() as session:
            infraction = Infraction(
                hubId=hub_id,
                moderatorId=str(self.user.id),
                userId=str(self.target_user.id if self.target_user else None),
                serverId=str(self.target_server.id) if self.target_server else None,
                serverName=(interaction.guild.name if interaction.guild else "Unknown"),
                reason=reason,
                type=InfractionType.WARNING,
                status=InfractionStatus.ACTIVE,
                notified=False,
                # Warnings expire after 30 days
                expiresAt=datetime.now() + timedelta(days=30),
                moderationCommand="warn",
                createdAt=datetime.now(),
                updatedAt=datetime.now(),
                logId="",  # TODO: Implement proper log ID generation
            )

            session.add(infraction)
            await session.commit()

            # Send confirmation
            embed = discord.Embed(
                title=f"{self.bot.emotes.edit_icon} Warning Issued",
                description=f"Successfully warned {self.target_user.mention}",
                color=discord.Color.orange(),
            )
            embed.add_field(name="Reason", value=reason, inline=False)
            embed.add_field(name="Moderator", value=self.user.mention, inline=True)
            embed.add_field(name="Infraction ID", value=infraction.id, inline=True)
            embed.set_footer(
                text=f"Expires: {infraction.expiresAt.strftime('%Y-%m-%d %H:%M:%S')}"
            )

            await interaction.followup.send(embed=embed, ephemeral=True)

    async def create_mute_modal(self, interaction: discord.Interaction) -> None:
        """Create and show a modal for collecting mute reason and duration."""
        modal = CustomModal(
            title="Mute User",
            options=[
                (
                    "reason",
                    TextInput(
                        label="Reason",
                        placeholder="Enter the reason for muting this user...",
                        style=discord.TextStyle.paragraph,
                        max_length=500,
                        required=True,
                    ),
                ),
                (
                    "duration",
                    TextInput(
                        label="Duration",
                        placeholder="Duration (e.g., 1d, 1w, 1m, 1y). Leave blank for permanent.",
                        max_length=10,
                        required=False,
                    ),
                ),
            ],
            callback=self.handle_mute_submission,
        )
        await interaction.response.send_modal(modal)

    async def handle_mute_submission(
        self, modal: CustomModal, interaction: discord.Interaction
    ) -> None:
        """Handle the submission of the mute modal."""
        hub_id = await self.get_hub_id_from_channel()
        if not hub_id:
            await interaction.followup.send(
                f"{self.bot.emotes.x_icon} This channel is not connected to any hub.",
                ephemeral=True,
            )
            return

        reason = modal.reason.value
        duration_str = modal.duration.value.strip() if modal.duration.value else ""

        # Parse duration
        duration_ms = parse_duration(duration_str) if duration_str else None
        expires_at = duration_to_datetime(duration_ms)
        ban_type = BanType.TEMPORARY if duration_ms else BanType.PERMANENT

        # Create the ban record (mute is implemented as a temporary ban)
        async with self.bot.db.get_session() as session:
            ban = Ban(
                userId=str(self.target_user.id),
                moderatorId=str(self.user.id),
                reason=reason,
                duration=duration_ms,
                expiresAt=expires_at,
                type=ban_type,
                status=BanStatus.ACTIVE,
            )

            session.add(ban)
            await session.commit()

            # Send confirmation
            embed = discord.Embed(
                title=f"{self.bot.emotes.clock_icon} User Muted",
                description=f"Successfully muted {self.target_user.mention}",
                color=discord.Color.orange(),
            )
            embed.add_field(name="Reason", value=reason, inline=False)
            embed.add_field(name="Moderator", value=self.user.mention, inline=True)
            embed.add_field(name="Ban ID", value=ban.id, inline=True)

            if expires_at:
                embed.add_field(name="Duration", value=duration_str, inline=True)
                embed.set_footer(
                    text=f"Expires: {expires_at.strftime('%Y-%m-%d %H:%M:%S')}"
                )
            else:
                embed.add_field(name="Duration", value="Permanent", inline=True)

            await interaction.followup.send(embed=embed, ephemeral=True)

    async def create_ban_modal(self, interaction: discord.Interaction) -> None:
        """Create and show a modal for collecting ban reason and duration."""
        # Determine if we're banning a user or server
        target_type = "User" if hasattr(self, '_target_selected') and self._target_selected == 'user' else "User/Server"

        modal = CustomModal(
            title=f"Ban {target_type}",
            options=[
                (
                    "reason",
                    TextInput(
                        label="Reason",
                        placeholder=f"Enter the reason for banning this {target_type.lower()}...",
                        style=discord.TextStyle.paragraph,
                        max_length=500,
                        required=True,
                    ),
                ),
                (
                    "duration",
                    TextInput(
                        label="Duration",
                        placeholder="Duration (e.g., 1d, 1w, 1m, 1y). Leave blank for permanent.",
                        max_length=10,
                        required=False,
                    ),
                ),
            ],
            callback=self.handle_ban_submission,
        )
        await interaction.response.send_modal(modal)

    async def handle_ban_submission(
        self, modal: CustomModal, interaction: discord.Interaction
    ) -> None:
        """Handle the submission of the ban modal."""
        try:
            hub_id = await self.get_hub_id_from_channel()
            if not hub_id:
                await interaction.followup.send(
                    f"{self.bot.emotes.x_icon} This channel is not connected to any hub.",
                    ephemeral=True,
                )
                return

            reason = modal.reason.value
            duration_str = modal.duration.value.strip() if modal.duration.value else ""

            # Parse duration
            duration_ms = parse_duration(duration_str) if duration_str else None
            expires_at = duration_to_datetime(duration_ms)
            ban_type = BanType.TEMPORARY if duration_ms else BanType.PERMANENT

            # Determine target type and create appropriate ban record
            async with self.bot.db.get_session() as session:
                # Check if we're banning a user or server based on target selection
                if hasattr(self, '_target_selected') and self._target_selected == 'server':
                    # Server ban using ServerBan model
                    server_ban = ServerBan(
                        serverId=str(self.target_server.id),
                        moderatorId=str(self.user.id),
                        reason=reason,
                        duration=duration_ms or 0,  # ServerBan requires int, not Optional[int]
                        expiresAt=expires_at or None,
                        type=ban_type,
                        status=BanStatus.ACTIVE,
                    )
                    session.add(server_ban)
                    await session.commit()

                    # Send confirmation
                    embed = discord.Embed(
                        title=f"{self.bot.emotes.hammer_icon} Server Banned",
                        description=f"Successfully banned server **{self.target_server.name}**",
                        color=discord.Color.red(),
                    )
                    embed.add_field(name="Reason", value=reason, inline=False)
                    embed.add_field(name="Moderator", value=self.user.mention, inline=True)
                    embed.add_field(name="Ban ID", value=server_ban.id, inline=True)
                else:
                    # User ban using Ban model
                    user_ban = Ban(
                        userId=str(self.target_user.id),
                        moderatorId=str(self.user.id),
                        reason=reason,
                        duration=duration_ms,
                        expiresAt=expires_at,
                        type=ban_type,
                        status=BanStatus.ACTIVE,
                    )
                    session.add(user_ban)
                    await session.commit()

                    # Send confirmation
                    embed = discord.Embed(
                        title=f"{self.bot.emotes.hammer_icon} User Banned",
                        description=f"Successfully banned {self.target_user.mention}",
                        color=discord.Color.red(),
                    )
                    embed.add_field(name="Reason", value=reason, inline=False)
                    embed.add_field(name="Moderator", value=self.user.mention, inline=True)
                    embed.add_field(name="Ban ID", value=user_ban.id, inline=True)

                if expires_at:
                    embed.add_field(name="Duration", value=duration_str, inline=True)
                    embed.set_footer(
                        text=f"Expires: {expires_at.strftime('%Y-%m-%d %H:%M:%S')}"
                    )
                else:
                    embed.add_field(name="Duration", value="Permanent", inline=True)

                await interaction.followup.send(embed=embed, ephemeral=True)

        except Exception as error:
            logger.error(f"Error creating ban: {error}")
            await interaction.followup.send(
                f"{self.bot.emotes.x_icon} An error occurred while banning the user.",
                ephemeral=True,
            )

    async def create_blacklist_embed(self, interaction: discord.Interaction) -> None:
        """Create and show an embed with blacklist options (User vs Server)."""
        embed = discord.Embed(
            title="Blacklist Options",
            description="Choose the type of blacklist action:",
            color=discord.Color.dark_red(),
        )
        embed.add_field(
            name="User Blacklist",
            value="Blacklist the user from all hubs",
            inline=False,
        )
        embed.add_field(
            name="Server Blacklist",
            value="Blacklist the entire server from all hubs",
            inline=False,
        )

        view = BlacklistView(self.bot, self.user, self.target_user, interaction.guild)
        await interaction.response.send_message(embed=embed, view=view, ephemeral=True)

    async def setup_options(self, ctx, message):
        options = []

        # Add delete option if message is provided
        if message != "N/A":
            options.append(
                discord.SelectOption(
                    emoji=self.bot.emotes.delete_icon,
                    label="Delete",
                    description="Delete the selected message across all connected hubs",
                    value="delete",
                )
            )

        # Add standard moderation options
        options.extend(
            [
                discord.SelectOption(
                    emoji=self.bot.emotes.edit_icon,
                    label="Warn",
                    description="Issue a warning to the selected user",
                    value="warn",
                ),
                discord.SelectOption(
                    emoji=self.bot.emotes.clock_icon,
                    label="Mute",
                    description="Mute a user from the specified hub",
                    value="mute",
                ),
                discord.SelectOption(
                    emoji=self.bot.emotes.hammer_icon,
                    label="Ban",
                    description="Ban a user/server from the specified hub",
                    value="ban",
                ),
            ]
        )

        # Add blacklist option for staff users only
        if await is_interchat_staff(ctx, self.user):
            options.append(
                discord.SelectOption(
                    emoji=self.bot.emotes.gear_icon,
                    label="Blacklist",
                    description="Issue an interchat wide blacklist (user/server)",
                    value="blacklist",
                ),
            )

        self.on_submit.options = options

    @discord_select(
        placeholder="Select an action",
        options=[
            discord.SelectOption(label="Loading..", value="NONE"),
        ],
        max_values=1,
        min_values=1,
    )
    async def on_submit(
        self, interaction: discord.Interaction, select: discord.ui.Select
    ):
        """Handle moderation action selection."""
        action = select.values[0]

        if action == "delete":
            # TODO: How do you delete a message across all hubs? @dev
            await interaction.response.send_message(
                f"{self.bot.emotes.x_icon} Message deletion across hubs is not yet implemented.",
                ephemeral=True,
            )
            return

        # Check if we need target selection (both user and server available)
        if self.target_user and self.target_server and not hasattr(self, '_target_selected'):
            await self.show_target_selection(interaction, action)
            return

        # Proceed with the selected action
        if action == "warn":
            await self.create_infraction_modal(interaction)
            return

        elif action == "mute":
            await self.create_mute_modal(interaction)

        elif action == "ban":
            await self.create_ban_modal(interaction)

        elif action == "blacklist":
            await self.create_blacklist_embed(interaction)

    async def show_target_selection(self, interaction: discord.Interaction, action: str):
        """Show target selection UI after action is selected."""
        from utils.modules.ui.ModerationViews import TargetSelectionView

        # Store the selected action for later use
        self.selected_action = action

        view = TargetSelectionView(
            self.bot,
            self.user,
            self.target_user,
            self.target_server,
            self.target_message,
            self.constants,
            self.locale,
            selected_action=action
        )

        embed = discord.Embed(
            title="Target Selection",
            description=f"Who do you want to {action}?",
            color=discord.Color.blue(),
        )
        embed.add_field(
            name="User", value=f"{self.target_user.mention} (`{self.target_user.id}`)", inline=True
        )
        embed.add_field(
            name="Server",
            value=f"**{self.target_server.name}** (`{self.target_server.id}`)",
            inline=True,
        )

        await interaction.response.edit_message(embed=embed, view=view)

    @button(label="History", style=discord.ButtonStyle.grey)
    async def history_callback(self, interaction: discord.Interaction, button: Button):
        async with self.bot.db.get_session() as session:
            stmt = select(Infraction).where(
                Infraction.userId == str(self.target_user.id)
            )
            result = (await session.execute(stmt)).scalar()

        if not result:
            return await interaction.response.send_message(
                f"{self.bot.emotes.x_icon} No moderation history found for {self.target_user.mention}.",
                ephemeral=True,
            )

        logs = [log.to_dict() for log in result]
        view = Paginator(
            self.bot, self.user, self.target_user, self.constants, self.locale, []
        )
        embed = view.get_embed()
        message = await interaction.response.send_message(
            embed=embed, view=view, ephemeral=True
        )
        view.fetch_message(message)


class BlacklistView(View):
    """View for handling blacklist actions with User vs Server options."""

    def __init__(self, bot, user, target_user, guild):
        super().__init__(timeout=300)
        self.bot = bot
        self.user = user
        self.target_user = target_user
        self.guild = guild

    @button(label="Blacklist User", style=discord.ButtonStyle.danger, emoji="👤")
    async def blacklist_user(self, interaction: discord.Interaction, button: Button):
        """Handle user blacklist button click."""
        if not await interaction_check(interaction, self.user, interaction.user):
            return

        modal = CustomModal(
            title="Blacklist User",
            options=[
                (
                    "reason",
                    TextInput(
                        label="Reason",
                        placeholder="Enter the reason for blacklisting this user...",
                        style=discord.TextStyle.paragraph,
                        max_length=500,
                        required=True,
                    ),
                ),
                (
                    "duration",
                    TextInput(
                        label="Duration",
                        placeholder="Duration (e.g., 1d, 1w, 1m, 1y). Leave blank for permanent.",
                        max_length=10,
                        required=False,
                    ),
                ),
            ],
            callback=self.handle_user_blacklist_submission,
        )
        await interaction.response.send_modal(modal)

    @button(label="Blacklist Server", style=discord.ButtonStyle.danger, emoji="🏢")
    async def blacklist_server(self, interaction: discord.Interaction, button: Button):
        """Handle server blacklist button click."""
        if not await interaction_check(interaction, self.user, interaction.user):
            return

        modal = CustomModal(
            title="Blacklist Server",
            options=[
                (
                    "reason",
                    TextInput(
                        label="Reason",
                        placeholder="Enter the reason for blacklisting this server...",
                        style=discord.TextStyle.paragraph,
                        max_length=500,
                        required=True,
                    ),
                ),
                (
                    "duration",
                    TextInput(
                        label="Duration",
                        placeholder="Duration (e.g., 1d, 1w, 1m, 1y). Leave blank for permanent.",
                        max_length=10,
                        required=False,
                    ),
                ),
            ],
            callback=self.handle_server_blacklist_submission,
        )
        await interaction.response.send_modal(modal)

    def parse_duration(self, duration_str: str) -> Optional[int]:
        """Parse duration string like '1d', '2w', '3m', '1y' into milliseconds."""
        if not duration_str or duration_str.lower() in ["permanent", "perm", ""]:
            return None

        match = re.match(r"^(\d+)([dwmy])$", duration_str.lower().strip())
        if not match:
            return None

        amount, unit = match.groups()
        amount = int(amount)

        if unit == "d":  # days
            return amount * 24 * 60 * 60 * 1000
        elif unit == "w":  # weeks
            return amount * 7 * 24 * 60 * 60 * 1000
        elif unit == "m":  # months (30 days)
            return amount * 30 * 24 * 60 * 60 * 1000
        elif unit == "y":  # years (365 days)
            return amount * 365 * 24 * 60 * 60 * 1000

        return None

    def duration_to_datetime(self, duration_ms: Optional[int]) -> Optional[datetime]:
        """Convert duration in milliseconds to expiration datetime."""
        if duration_ms is None:
            return None
        return datetime.now() + timedelta(milliseconds=duration_ms)

    async def handle_user_blacklist_submission(
        self, modal: CustomModal, interaction: discord.Interaction
    ) -> None:
        """Handle the submission of the user blacklist modal."""
        try:
            reason = modal.reason.value
            duration_str = modal.duration.value.strip() if modal.duration.value else ""

            # Parse duration
            duration_ms = parse_duration(duration_str) if duration_str else None
            expires_at = duration_to_datetime(duration_ms)
            ban_type = BanType.TEMPORARY if duration_ms else BanType.PERMANENT

            # Create the blacklist infraction record (bot-wide)
            async with self.bot.db.get_session() as session:
                infraction = Infraction(
                    userId=str(self.target_user.id),
                    moderatorId=str(self.user.id),
                    reason=reason,
                    expiresAt=expires_at or datetime.max,  # Use max datetime for permanent
                    type=InfractionType.BLACKLIST,
                    status=InfractionStatus.ACTIVE,
                    hubId="GLOBAL",  # Bot-wide blacklist identifier
                    serverId=None,
                    serverName=None,
                    moderationCommand="blacklist",
                    logId="", # TODO: Implement proper log ID generation or just get rid of modlogs
                )

                session.add(infraction)
                await session.commit()

                # Send confirmation
                embed = discord.Embed(
                    title=f"{self.bot.emotes.ban_icon} User Blacklisted",
                    description=f"Successfully blacklisted {self.target_user.mention}",
                    color=discord.Color.dark_red(),
                )
                embed.add_field(name="Reason", value=reason, inline=False)
                embed.add_field(name="Moderator", value=self.user.mention, inline=True)
                embed.add_field(name="Infraction ID", value=infraction.id, inline=True)

                if expires_at:
                    embed.add_field(name="Duration", value=duration_str, inline=True)
                    embed.set_footer(
                        text=f"Expires: {expires_at.strftime('%Y-%m-%d %H:%M:%S')}"
                    )
                else:
                    embed.add_field(name="Duration", value="Permanent", inline=True)

                await interaction.followup.send(embed=embed, ephemeral=True)

        except Exception as error:
            logger.error(f"Error creating user blacklist: {error}")
            await interaction.followup.send(
                f"{self.bot.emotes.x_icon} An error occurred while blacklisting the user.",
                ephemeral=True,
            )

    async def handle_server_blacklist_submission(
        self, modal: CustomModal, interaction: discord.Interaction
    ) -> None:
        """Handle the submission of the server blacklist modal."""
        try:
            reason = modal.reason.value
            duration_str = modal.duration.value.strip() if modal.duration.value else ""

            # Parse duration
            duration_ms = parse_duration(duration_str) if duration_str else None
            expires_at = duration_to_datetime(duration_ms)
            ban_type = BanType.TEMPORARY if duration_ms else BanType.PERMANENT

            # Create the server blacklist infraction record (bot-wide)
            async with self.bot.db.get_session() as session:
                infraction = Infraction(
                    userId=None,  # No user for server blacklist
                    moderatorId=str(self.user.id),
                    reason=reason,
                    expiresAt=expires_at or datetime.max,  # Use max datetime for permanent
                    type=InfractionType.BLACKLIST,
                    status=InfractionStatus.ACTIVE,
                    hubId="GLOBAL",  # Bot-wide blacklist identifier
                    serverId=str(self.target_server.id),
                    serverName=self.target_server.name,
                    moderationCommand="blacklist",
                    logId="",
                )

                session.add(infraction)
                await session.commit()

                # Send confirmation
                embed = discord.Embed(
                    title=f"{self.bot.emotes.ban_icon} Server Blacklisted",
                    description=f"Successfully blacklisted server **{self.target_server.name}**",
                    color=discord.Color.dark_red(),
                )
                embed.add_field(name="Reason", value=reason, inline=False)
                embed.add_field(name="Moderator", value=self.user.mention, inline=True)
                embed.add_field(name="Infraction ID", value=infraction.id, inline=True)

                if expires_at:
                    embed.add_field(name="Duration", value=duration_str, inline=True)
                    embed.set_footer(
                        text=f"Expires: {expires_at.strftime('%Y-%m-%d %H:%M:%S')}"
                    )
                else:
                    embed.add_field(name="Duration", value="Permanent", inline=True)

                await interaction.followup.send(embed=embed, ephemeral=True)

        except Exception as error:
            logger.error(f"Error creating server blacklist: {error}")
            await interaction.followup.send(
                f"{self.bot.emotes.x_icon} An error occurred while blacklisting the server.",
                ephemeral=True,
            )


class HubModeration(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.constants = InterchatConstants()
        self.locale = "en"

    @commands.hybrid_group()
    async def moderation(self, ctx: commands.Context[commands.Bot]):
        pass

    @moderation.command(
        name="panel",
        description="Open the moderation panel for users, messages, or servers",
        extras={"category": "Hubs"},
    )
    async def panel(
        self,
        ctx: commands.Context[commands.Bot],
        *,
        user: discord.User = None,
        message_id: Optional[str] = None,
        server: discord.Guild = None,
    ):
        # Handle message parameter priority logic
        message = None
        if message_id is not None:
            # Message ID provided - ignore user and server parameters
            original_message = await fetch_original_message(self.bot, message_id)
            if original_message:
                message_content = original_message.content
                # Extract user and server info from the original message
                try:
                    target_user = await self.bot.fetch_user(
                        int(original_message.authorId)
                    )
                    target_guild = await self.bot.fetch_guild(
                        int(original_message.guildId)
                    )
                    # Try to fetch the actual Discord message for UI purposes
                    try:
                        channel = await self.bot.fetch_channel(
                            int(original_message.channelId)
                        )
                        message = await channel.fetch_message(int(message_id))
                    except (discord.NotFound, discord.Forbidden):
                        message = None
                except (discord.NotFound, ValueError):
                    target_user = None
                    target_guild = None
            else:
                # If not found in database, try to fetch directly from Discord
                try:
                    message = await ctx.channel.fetch_message(int(message_id))
                    message_content = message.content
                    target_user = message.author
                    target_guild = message.guild
                except (discord.NotFound, ValueError):
                    return await ctx.send("Message not found.")

            message_content = (
                message_content if "message_content" in locals() else "N/A"
            )

            # Show target selection if both user and server are available
            if target_user and target_guild:
                view = TargetSelectionView(
                    self.bot,
                    ctx.author,
                    target_user,
                    target_guild,
                    message,
                    self.constants,
                    self.locale,
                )
                embed = discord.Embed(
                    title="Target Selection",
                    description="Who do you want to take action on?",
                    color=discord.Color.blue(),
                )
                embed.add_field(
                    name="User",
                    value=f"{target_user.mention} (`{target_user.id}`)",
                    inline=True,
                )
                embed.add_field(
                    name="Server",
                    value=f"**{target_guild.name}** (`{target_guild.id}`)",
                    inline=True,
                )
                await ctx.send(embed=embed, view=view)
                return

            # Proceed with single target
            final_user = target_user
            final_server = target_guild

        else:
            # No message provided - check user/server parameters
            if user is None and server is None:
                return await ctx.send(
                    "Error: You must provide either a message, user, or server parameter."
                )

            message_content = "N/A"
            final_user = user
            final_server = server

            # Show hub selection for user/server-only actions
            user_hubs = await get_user_moderated_hubs(self.bot, str(ctx.author.id))
            if not user_hubs:
                return await ctx.send(
                    "You don't have moderation permissions in any hubs."
                )

            # Show hub selection first when both user and server are provided
            # Target selection will happen after action is selected
            view = HubSelectionView(
                self.bot,
                ctx.author,
                final_user,
                final_server,
                user_hubs,
                self.constants,
                self.locale,
            )
            embed = discord.Embed(
                title="Hub Selection",
                description="Select a hub to perform moderation actions:",
                color=discord.Color.orange(),
            )
            embed.add_field(name="Hub", value="N/A - Select from dropdown", inline=True)
            if final_user:
                embed.add_field(
                    name="Target User",
                    value=f"{final_user.mention} (`{final_user.id}`)",
                    inline=True,
                )
            if final_server:
                embed.add_field(
                    name="Target Server",
                    value=f"**{final_server.name}** (`{final_server.id}`)",
                    inline=True,
                )

            await ctx.send(embed=embed, view=view)
            return

        # Fallback case - direct moderation panel (should not reach here with new logic)
        # This preserves existing functionality for any edge cases
        embed = create_embed(
            self.locale,
            title_key="modPanel.embed.title",
            description_key="modPanel.embed.description",
            fields=[
                {
                    "name_key": "modPanel.embed.fields.data.name",
                    "value_key": "modPanel.embed.fields.data.value",
                },
                {
                    "name_key": "modPanel.embed.fields.user.name",
                    "value_key": "modPanel.embed.fields.user.value",
                },
            ],
            messageContent=message_content,
            messageLink=final_user if message is None else message,
            serverName=final_server.name if final_server else "InterChat",
            serverId=str(final_server.id) if final_server else "1390620814478544946",
            hubName="InterChat",
            userId=str(final_user.id) if final_user else "934537337113804891",
            reputation=0,
            infractions=2,
        )
        embed.set_author(
            name=f"@{ctx.author.name}", icon_url=ctx.author.display_avatar.url
        )
        view = ModerationButtons(
            self.bot,
            ctx.author,
            self.constants,
            self.locale,
            final_user,
            message,
            str(ctx.channel.id),
            target_server=final_server,
        )
        await view.setup_options(ctx, message_content)
        await ctx.send(embed=embed, view=view)


async def setup(bot):
    await bot.add_cog(HubModeration(bot))
