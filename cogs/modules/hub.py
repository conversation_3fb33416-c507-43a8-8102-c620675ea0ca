from datetime import datetime
from discord import Thread
from unittest.mock import Mock

import discord
from discord.ext import commands
from sqlalchemy import select
from utils.modules.core.db.models import Connection, Hub
from utils.constants import InterchatConstants
from utils.utils import check_user

from typing import TYPE_CHECKING, Optional

if TYPE_CHECKING:
    from main import Bot


class Hubs(commands.Cog):
    def __init__(self, bot):
        self.bot: Bot = bot
        self.constants = InterchatConstants()

    @commands.hybrid_command(
        name="hubs", description="View InterChat hubs", extras={"category": "Hubs"}
    )
    @check_user()
    async def hubs(self, ctx: commands.Context[commands.Bot]):
        async with self.bot.db.get_session() as session:
            hubs = (await session.execute(select(Hub))).scalars().all()
            if not hubs:
                await ctx.send(content="no hubs found")
                return

            content = ""
            for hub in hubs:
                content += f"{hub.name} ({hub.id}): {hub.shortDescription} | {hub.description}\n"
            await ctx.send(content=content)

    @commands.hybrid_group()
    async def hub(self, ctx: commands.Context[commands.Bot]):
        pass

    @hub.command(
        name="create",
        description="Create an InterChat hub",
        extras={"category": "Hubs"},
    )
    @check_user()
    async def create_hub(
        self,
        ctx: commands.Context[commands.Bot],
        hub_name: str,
        short_description: str,
        description: str,
        icon_url: str,
    ):  # i'd do this interactivley tbh with a view
        async with self.bot.db.get_session() as session:
            hub = Hub(
                name=hub_name,
                shortDescription=short_description,
                description=description,
                ownerId=str(ctx.author.id),
                iconUrl=icon_url,
                rules=[],
                createdAt=datetime.now(),
                updatedAt=datetime.now(),
                lastActive=datetime.now(),
            )
            session.add(hub)
            await ctx.send(content="hub created")
            return

        await ctx.send(content="Failed to create hub. Please try again later.")

    @hub.command(
        name="configure",
        description="Configure your InterChat hub",
        extras={"category": "Hubs"},
    )
    @check_user()
    async def configure(self, ctx: commands.Context[commands.Bot], hub): ...

    @hub.command(name='delete', description='🗑️ Delete an InterChat hub', extras={'category': 'Hubs'})
    async def delete_hub(self, ctx: commands.Context[commands.Bot], hub: str):
        async with self.bot.db.get_session() as session:
            hub_obj = (await session.execute(select(Hub).where(Hub.name == hub))).scalar_one_or_none()
            if not hub_obj:
                await ctx.send(content="Hub not found")
                return

            if str(hub_obj.ownerId) != str(ctx.author.id):
                raise discord.Forbidden(Mock(403), "You are not the owner of this hub.")

            await session.delete(hub_obj)
            await session.commit()
            await ctx.send(content="Hub deleted successfully")

    async def get_webhook(self, channel: discord.ForumChannel | discord.TextChannel):
        webhooks = await channel.webhooks()
        for webhook in webhooks:
            if self.bot.user and webhook.user and (webhook.user.id == self.bot.user.id):
                return webhook
        return None

    async def get_or_create_webhook(self, channel: discord.abc.GuildChannel):
        if isinstance(channel, discord.Thread):
            parent = channel.parent
            if parent and isinstance(
                parent, (discord.TextChannel, discord.ForumChannel)
            ):
                webhook = await self.get_webhook(parent)
                return webhook or await parent.create_webhook(name="MyWebhook")
            return None

        elif isinstance(channel, (discord.TextChannel)):
            webhook = await self.get_webhook(channel)
            return webhook or await channel.create_webhook(name="MyWebhook")

        return None
    
    @commands.hybrid_command()
    async def join_hub(
        self,
        ctx: commands.Context[commands.Bot],
        hub_id: str,
        channel: Optional[discord.TextChannel | discord.Thread] = None,
    ):
        selected_channel = channel or ctx.channel

        # Join a hub
        async with self.bot.db.get_session() as session:
            hub = (
                await session.execute(select(Hub).where(Hub.id == hub_id))
            ).scalar_one_or_none()
            if not hub:
                await ctx.send(content="Hub not found")
                return
            if not ctx.guild:
                await ctx.send(content="This command can only be used in a server.")
                return

            # TODO: Permission checks for channel

            webhook = await self.get_or_create_webhook(selected_channel)
            if not webhook:
                await ctx.send(content="Failed to create webhook")
                return

            # create connection in db
            connection = Connection(
                hubId=hub.id,
                channelId=str(selected_channel.id),
                webhookUrl=webhook.url,
                parentId=isinstance(selected_channel, discord.Thread)
                and selected_channel.parent
                and str(selected_channel.parent.id)
                or None,
                invite=None,
                serverId=str(ctx.guild.id),
                createdAt=datetime.now(),
                lastActive=datetime.now(),
            )
            session.add(connection)
            await session.commit()
            await ctx.send(content="Connection created")
            await ctx.send(content=f"You have joined the hub: {hub.name}")


async def setup(bot: commands.Bot):
    await bot.add_cog(Hubs(bot))
